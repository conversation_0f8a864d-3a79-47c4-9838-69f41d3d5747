package services

import (
	"context"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// UserService handles user business logic
type UserService struct {
	userRepo repositories.UserRepository
	roleRepo repositories.RoleRepository
	logger   *logrus.Logger
	db       *gorm.DB
}

func NewUserService(userRepo repositories.UserRepository, roleRepo repositories.RoleRepository, logger *logrus.Logger) *UserService {
	return &UserService{
		userRepo: userRepo,
		roleRepo: roleRepo,
		logger:   logger,
	}
}

// NewUserServiceWithDB creates a new UserService with database access
func NewUserServiceWithDB(userRepo repositories.UserRepository, roleRepo repositories.RoleRepository, logger *logrus.Logger, db *gorm.DB) *UserService {
	return &UserService{
		userRepo: userRepo,
		roleRepo: roleRepo,
		logger:   logger,
		db:       db,
	}
}

// User methods
func (s *UserService) GetUsers(ctx context.Context, merchantID uuid.UUID, filters types.UserFilters) (*types.UsersResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	// Set default sorting
	if filters.SortBy == "" {
		filters.SortBy = "first_name"
	}
	if filters.SortOrder == "" {
		filters.SortOrder = "asc"
	}

	users, total, err := s.userRepo.GetByMerchantID(ctx, merchantID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get users")
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// Convert to response format
	userResponses := make([]types.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = s.convertUserToResponse(user)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.UsersResponse{
		Data:       userResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *UserService) GetUserByID(ctx context.Context, merchantID, userID uuid.UUID) (*types.UserResponse, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get user by ID")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Verify user belongs to merchant
	if user.MerchantID != merchantID {
		return nil, fmt.Errorf("user not found")
	}

	response := s.convertUserToResponse(*user)
	return &response, nil
}

func (s *UserService) CreateUser(ctx context.Context, merchantID uuid.UUID, req types.CreateUserRequest) (*types.UserResponse, error) {
	// Check if email already exists
	existingUser, _ := s.userRepo.GetByEmail(ctx, req.Email)
	if existingUser != nil {
		return nil, fmt.Errorf("user with email %s already exists", req.Email)
	}

	// Verify role exists and belongs to merchant
	role, err := s.roleRepo.GetByID(ctx, req.RoleID)
	if err != nil || role.MerchantID != merchantID {
		return nil, fmt.Errorf("invalid role")
	}

	user := &models.User{
		MerchantID:       merchantID,
		BranchID:         req.BranchID,
		Email:            req.Email,
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		Phone:            req.Phone,
		RoleID:           req.RoleID,
		Position:         req.Position,
		Department:       req.Department,
		EmployeeID:       req.EmployeeID,
		AvatarURL:        req.AvatarURL,
		Status:           types.UserStatusActive,
		Address:          s.convertAddressRequestToModel(req.Address),
		EmergencyContact: s.convertContactInfoRequestToModel(req.EmergencyContact),
	}

	// Set password
	if err := user.SetPassword(req.Password); err != nil {
		s.logger.WithError(err).Error("Failed to hash password")
		return nil, fmt.Errorf("failed to create user")
	}

	// Set hire date
	if req.HireDate != nil {
		user.HireDate = *req.HireDate
	} else {
		user.HireDate = time.Now()
	}

	// Set salary/hourly rate
	if req.Salary != nil {
		user.Salary = req.Salary
	}
	if req.HourlyRate != nil {
		user.HourlyRate = req.HourlyRate
	}

	if err := s.userRepo.Create(ctx, user); err != nil {
		s.logger.WithError(err).Error("Failed to create user")
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Reload user with relationships
	createdUser, err := s.userRepo.GetByID(ctx, user.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get created user")
		return nil, fmt.Errorf("failed to get created user")
	}

	response := s.convertUserToResponse(*createdUser)
	return &response, nil
}

func (s *UserService) UpdateUser(ctx context.Context, merchantID, userID uuid.UUID, req types.UpdateUserRequest) (*types.UserResponse, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get user")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Verify user belongs to merchant
	if user.MerchantID != merchantID {
		return nil, fmt.Errorf("user not found")
	}

	// Update fields
	if req.FirstName != nil {
		user.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		user.LastName = *req.LastName
	}
	if req.Phone != nil {
		user.Phone = *req.Phone
	}
	if req.RoleID != nil {
		// Verify role exists and belongs to merchant
		role, err := s.roleRepo.GetByID(ctx, *req.RoleID)
		if err != nil || role.MerchantID != merchantID {
			return nil, fmt.Errorf("invalid role")
		}
		user.RoleID = *req.RoleID
	}
	if req.Position != nil {
		user.Position = *req.Position
	}
	if req.Department != nil {
		user.Department = *req.Department
	}
	if req.EmployeeID != nil {
		user.EmployeeID = *req.EmployeeID
	}
	if req.HireDate != nil {
		user.HireDate = *req.HireDate
	}
	if req.Salary != nil {
		user.Salary = req.Salary
	}
	if req.HourlyRate != nil {
		user.HourlyRate = req.HourlyRate
	}
	if req.BranchID != nil {
		user.BranchID = req.BranchID
	}
	if req.AvatarURL != nil {
		user.AvatarURL = *req.AvatarURL
	}
	if req.Status != nil {
		user.Status = *req.Status
	}

	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.WithError(err).Error("Failed to update user")
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Reload user with relationships
	updatedUser, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated user")
		return nil, fmt.Errorf("failed to get updated user")
	}

	response := s.convertUserToResponse(*updatedUser)
	return &response, nil
}

func (s *UserService) DeleteUser(ctx context.Context, merchantID, userID uuid.UUID) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get user")
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Verify user belongs to merchant
	if user.MerchantID != merchantID {
		return fmt.Errorf("user not found")
	}

	if err := s.userRepo.Delete(ctx, userID); err != nil {
		s.logger.WithError(err).Error("Failed to delete user")
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

func (s *UserService) UpdateUserStatus(ctx context.Context, merchantID, userID uuid.UUID, status string) (*types.UserResponse, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get user")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Verify user belongs to merchant
	if user.MerchantID != merchantID {
		return nil, fmt.Errorf("user not found")
	}

	if err := s.userRepo.UpdateStatus(ctx, userID, status); err != nil {
		s.logger.WithError(err).Error("Failed to update user status")
		return nil, fmt.Errorf("failed to update user status: %w", err)
	}

	// Reload user with relationships
	updatedUser, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated user")
		return nil, fmt.Errorf("failed to get updated user")
	}

	response := s.convertUserToResponse(*updatedUser)
	return &response, nil
}

// Role methods
func (s *UserService) GetRoles(ctx context.Context, merchantID uuid.UUID) (*types.RolesResponse, error) {
	roles, err := s.roleRepo.GetByMerchantID(ctx, merchantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get roles")
		return nil, fmt.Errorf("failed to get roles: %w", err)
	}

	// Convert to response format
	roleResponses := make([]types.RoleResponse, len(roles))
	for i, role := range roles {
		roleResponses[i] = s.convertRoleToResponse(role)
	}

	return &types.RolesResponse{
		Data:       roleResponses,
		Total:      int64(len(roleResponses)),
		Page:       1,
		Limit:      len(roleResponses),
		TotalPages: 1,
	}, nil
}

func (s *UserService) CreateRole(ctx context.Context, merchantID uuid.UUID, req types.CreateRoleRequest) (*types.RoleResponse, error) {
	role := &models.Role{
		MerchantID:  merchantID,
		Name:        req.Name,
		Description: req.Description,
		Permissions: req.Permissions,
		IsActive:    true,
	}

	if err := s.roleRepo.Create(ctx, role); err != nil {
		s.logger.WithError(err).Error("Failed to create role")
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	response := s.convertRoleToResponse(*role)
	return &response, nil
}

func (s *UserService) UpdateRole(ctx context.Context, merchantID, roleID uuid.UUID, req types.UpdateRoleRequest) (*types.RoleResponse, error) {
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get role")
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	// Verify role belongs to merchant
	if role.MerchantID != merchantID {
		return nil, fmt.Errorf("role not found")
	}

	// Update fields
	if req.Name != nil {
		role.Name = *req.Name
	}
	if req.Description != nil {
		role.Description = *req.Description
	}
	if req.Permissions != nil {
		role.Permissions = req.Permissions
	}
	if req.IsActive != nil {
		role.IsActive = *req.IsActive
	}

	if err := s.roleRepo.Update(ctx, role); err != nil {
		s.logger.WithError(err).Error("Failed to update role")
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	response := s.convertRoleToResponse(*role)
	return &response, nil
}

func (s *UserService) DeleteRole(ctx context.Context, merchantID, roleID uuid.UUID) error {
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get role")
		return fmt.Errorf("failed to get role: %w", err)
	}

	// Verify role belongs to merchant
	if role.MerchantID != merchantID {
		return fmt.Errorf("role not found")
	}

	if err := s.roleRepo.Delete(ctx, roleID); err != nil {
		s.logger.WithError(err).Error("Failed to delete role")
		return fmt.Errorf("failed to delete role: %w", err)
	}

	return nil
}

// Helper functions
func (s *UserService) convertUserToResponse(user models.User) types.UserResponse {
	var roleResponse *types.RoleResponse
	if user.Role.ID != uuid.Nil {
		role := s.convertRoleToResponse(user.Role)
		roleResponse = &role
	}

	return types.UserResponse{
		ID:               user.ID,
		MerchantID:       user.MerchantID,
		BranchID:         user.BranchID,
		Email:            user.Email,
		FirstName:        user.FirstName,
		LastName:         user.LastName,
		Phone:            user.Phone,
		AvatarURL:        user.AvatarURL,
		RoleID:           user.RoleID,
		Role:             roleResponse,
		Position:         user.Position,
		Department:       user.Department,
		EmployeeID:       user.EmployeeID,
		HireDate:         user.HireDate,
		Salary:           user.Salary,
		HourlyRate:       user.HourlyRate,
		Status:           user.Status,
		Address:          s.convertAddressModelToResponse(user.Address),
		EmergencyContact: s.convertContactInfoModelToResponse(user.EmergencyContact),
		LastLoginAt:      user.LastLoginAt,
		CreatedAt:        user.CreatedAt,
		UpdatedAt:        user.UpdatedAt,
	}
}

func (s *UserService) convertRoleToResponse(role models.Role) types.RoleResponse {
	return types.RoleResponse{
		ID:          role.ID,
		MerchantID:  role.MerchantID,
		Name:        role.Name,
		Description: role.Description,
		Permissions: role.Permissions,
		IsActive:    role.IsActive,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}
}

func (s *UserService) convertAddressRequestToModel(req types.AddressRequest) models.Address {
	return models.Address{
		Street:  req.Street,
		City:    req.City,
		State:   req.State,
		ZipCode: req.ZipCode,
		Country: req.Country,
	}
}

func (s *UserService) convertAddressModelToResponse(addr models.Address) types.AddressResponse {
	return types.AddressResponse{
		Street:  addr.Street,
		City:    addr.City,
		State:   addr.State,
		ZipCode: addr.ZipCode,
		Country: addr.Country,
	}
}

func (s *UserService) convertContactInfoRequestToModel(req types.ContactInfoRequest) models.ContactInfo {
	return models.ContactInfo{
		Name:         req.Name,
		Relationship: req.Relationship,
		Phone:        req.Phone,
		Email:        req.Email,
	}
}

func (s *UserService) convertContactInfoModelToResponse(contact models.ContactInfo) types.ContactInfoResponse {
	return types.ContactInfoResponse{
		Name:         contact.Name,
		Relationship: contact.Relationship,
		Phone:        contact.Phone,
		Email:        contact.Email,
	}
}

// Slug-based methods

// GetUsersBySlug gets users by shop and branch slug
func (s *UserService) GetUsersBySlug(ctx context.Context, shopSlug, branchSlug string, filters types.UserFilters) (*types.UsersResponse, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Use the existing GetUsers method with the merchant ID
	return s.GetUsers(ctx, branch.Shop.ID, filters)
}

// CreateUserBySlug creates a user by shop and branch slug
func (s *UserService) CreateUserBySlug(ctx context.Context, shopSlug, branchSlug string, req types.CreateUserRequest) (*types.UserResponse, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Set the branch ID in the request
	req.BranchID = &branch.ID

	// Use the existing CreateUser method with the merchant ID
	return s.CreateUser(ctx, branch.Shop.ID, req)
}

// GetUserBySlug gets a user by shop and branch slug
func (s *UserService) GetUserBySlug(ctx context.Context, shopSlug, branchSlug string, userID uuid.UUID) (*types.UserResponse, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Use the existing GetUserByID method with the merchant ID
	return s.GetUserByID(ctx, branch.Shop.ID, userID)
}

// UpdateUserBySlug updates a user by shop and branch slug
func (s *UserService) UpdateUserBySlug(ctx context.Context, shopSlug, branchSlug string, userID uuid.UUID, req types.UpdateUserRequest) (*types.UserResponse, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Use the existing UpdateUser method with the merchant ID
	return s.UpdateUser(ctx, branch.Shop.ID, userID, req)
}

// DeleteUserBySlug deletes a user by shop and branch slug
func (s *UserService) DeleteUserBySlug(ctx context.Context, shopSlug, branchSlug string, userID uuid.UUID) error {
	if s.db == nil {
		return fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return fmt.Errorf("branch not found")
	}

	// Use the existing DeleteUser method with the merchant ID
	return s.DeleteUser(ctx, branch.Shop.ID, userID)
}

// UpdateUserStatusBySlug updates user status by shop and branch slug
func (s *UserService) UpdateUserStatusBySlug(ctx context.Context, shopSlug, branchSlug string, userID uuid.UUID, status string) (*types.UserResponse, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Use the existing UpdateUserStatus method with the merchant ID
	return s.UpdateUserStatus(ctx, branch.Shop.ID, userID, status)
}

// GetRolesBySlug gets roles by shop and branch slug
func (s *UserService) GetRolesBySlug(ctx context.Context, shopSlug, branchSlug string) (*types.RolesResponse, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// For now, return suggested default roles for getting started
	// In a real implementation, this would fetch from the database
	defaultRoles := []types.RoleResponse{
		{
			ID:          uuid.New(),
			MerchantID:  branch.Shop.ID,
			Name:        "Owner/Manager",
			Description: "Full access to all restaurant operations and settings",
			Permissions: []string{
				"dashboard.view", "analytics.view", "analytics.export",
				"staff.view", "staff.create", "staff.edit", "staff.delete", "staff.manage_roles", "staff.manage_schedule",
				"orders.view", "orders.create", "orders.edit", "orders.cancel", "orders.refund", "orders.kitchen_display",
				"menu.view", "menu.create", "menu.edit", "menu.delete", "menu.manage_categories", "menu.manage_pricing",
				"tables.view", "tables.manage", "tables.configure",
				"reservations.view", "reservations.create", "reservations.edit", "reservations.cancel",
				"inventory.view", "inventory.manage", "inventory.purchase_orders", "inventory.suppliers", "inventory.stock_alerts",
				"customers.view", "customers.manage", "customers.loyalty",
				"payments.process", "payments.refunds", "financial.view_reports", "financial.manage_taxes",
				"settings.shop", "settings.system", "settings.integrations",
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          uuid.New(),
			MerchantID:  branch.Shop.ID,
			Name:        "Assistant Manager",
			Description: "Manage daily operations, staff, and customer service",
			Permissions: []string{
				"dashboard.view", "analytics.view",
				"staff.view", "staff.create", "staff.edit", "staff.manage_schedule",
				"orders.view", "orders.create", "orders.edit", "orders.cancel", "orders.kitchen_display",
				"menu.view", "menu.edit", "menu.manage_pricing",
				"tables.view", "tables.manage",
				"reservations.view", "reservations.create", "reservations.edit", "reservations.cancel",
				"inventory.view", "inventory.manage", "inventory.stock_alerts",
				"customers.view", "customers.manage", "customers.loyalty",
				"payments.process", "payments.refunds",
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          uuid.New(),
			MerchantID:  branch.Shop.ID,
			Name:        "Server/Waiter",
			Description: "Take orders, manage tables, and serve customers",
			Permissions: []string{
				"dashboard.view",
				"orders.view", "orders.create", "orders.edit",
				"menu.view",
				"tables.view", "tables.manage",
				"reservations.view", "reservations.create", "reservations.edit",
				"customers.view",
				"payments.process",
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          uuid.New(),
			MerchantID:  branch.Shop.ID,
			Name:        "Kitchen Staff/Chef",
			Description: "Manage kitchen operations, menu items, and inventory",
			Permissions: []string{
				"dashboard.view",
				"orders.view", "orders.kitchen_display",
				"menu.view", "menu.create", "menu.edit", "menu.manage_categories",
				"inventory.view", "inventory.manage", "inventory.stock_alerts",
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          uuid.New(),
			MerchantID:  branch.Shop.ID,
			Name:        "Cashier",
			Description: "Process payments and handle customer transactions",
			Permissions: []string{
				"dashboard.view",
				"orders.view", "orders.create",
				"menu.view",
				"customers.view",
				"payments.process", "payments.refunds",
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          uuid.New(),
			MerchantID:  branch.Shop.ID,
			Name:        "Host/Hostess",
			Description: "Manage reservations, greet customers, and assign tables",
			Permissions: []string{
				"dashboard.view",
				"tables.view", "tables.manage",
				"reservations.view", "reservations.create", "reservations.edit", "reservations.cancel",
				"customers.view",
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          uuid.New(),
			MerchantID:  branch.Shop.ID,
			Name:        "Inventory Manager",
			Description: "Manage inventory, suppliers, and purchase orders",
			Permissions: []string{
				"dashboard.view", "analytics.view",
				"inventory.view", "inventory.manage", "inventory.purchase_orders", "inventory.suppliers", "inventory.stock_alerts",
				"menu.view", "menu.manage_pricing",
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	return &types.RolesResponse{
		Data:       defaultRoles,
		Total:      int64(len(defaultRoles)),
		Page:       1,
		Limit:      len(defaultRoles),
		TotalPages: 1,
	}, nil
}

// CreateRoleBySlug creates a role by shop and branch slug
func (s *UserService) CreateRoleBySlug(ctx context.Context, shopSlug, branchSlug string, req types.CreateRoleRequest) (*types.RoleResponse, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Use the existing CreateRole method with the merchant ID
	return s.CreateRole(ctx, branch.Shop.ID, req)
}

// UpdateRoleBySlug updates a role by shop and branch slug
func (s *UserService) UpdateRoleBySlug(ctx context.Context, shopSlug, branchSlug string, roleID uuid.UUID, req types.UpdateRoleRequest) (*types.RoleResponse, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Use the existing UpdateRole method with the merchant ID
	return s.UpdateRole(ctx, branch.Shop.ID, roleID, req)
}

// DeleteRoleBySlug deletes a role by shop and branch slug
func (s *UserService) DeleteRoleBySlug(ctx context.Context, shopSlug, branchSlug string, roleID uuid.UUID) error {
	if s.db == nil {
		return fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	branch, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return fmt.Errorf("branch not found")
	}

	// Use the existing DeleteRole method with the merchant ID
	return s.DeleteRole(ctx, branch.Shop.ID, roleID)
}

// GetPermissionsBySlug gets permissions by shop and branch slug
func (s *UserService) GetPermissionsBySlug(ctx context.Context, shopSlug, branchSlug string) (interface{}, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// First, get the branch by slug to get the merchant ID
	shopRepo := repositories.NewShopRepository(s.db)
	_, err := shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Comprehensive permissions for restaurant management
	permissionsData := []map[string]interface{}{
		// Dashboard & Analytics
		{"id": "dashboard.view", "name": "dashboard.view", "description": "View dashboard and overview", "category": "Dashboard", "isActive": true},
		{"id": "analytics.view", "name": "analytics.view", "description": "View analytics and reports", "category": "Analytics", "isActive": true},
		{"id": "analytics.export", "name": "analytics.export", "description": "Export analytics reports", "category": "Analytics", "isActive": true},

		// Staff Management
		{"id": "staff.view", "name": "staff.view", "description": "View staff members", "category": "Staff", "isActive": true},
		{"id": "staff.create", "name": "staff.create", "description": "Create new staff members", "category": "Staff", "isActive": true},
		{"id": "staff.edit", "name": "staff.edit", "description": "Edit staff member details", "category": "Staff", "isActive": true},
		{"id": "staff.delete", "name": "staff.delete", "description": "Delete staff members", "category": "Staff", "isActive": true},
		{"id": "staff.manage_roles", "name": "staff.manage_roles", "description": "Manage staff roles and permissions", "category": "Staff", "isActive": true},
		{"id": "staff.manage_schedule", "name": "staff.manage_schedule", "description": "Manage staff schedules", "category": "Staff", "isActive": true},

		// Order Management
		{"id": "orders.view", "name": "orders.view", "description": "View orders", "category": "Orders", "isActive": true},
		{"id": "orders.create", "name": "orders.create", "description": "Create new orders", "category": "Orders", "isActive": true},
		{"id": "orders.edit", "name": "orders.edit", "description": "Edit existing orders", "category": "Orders", "isActive": true},
		{"id": "orders.cancel", "name": "orders.cancel", "description": "Cancel orders", "category": "Orders", "isActive": true},
		{"id": "orders.refund", "name": "orders.refund", "description": "Process order refunds", "category": "Orders", "isActive": true},
		{"id": "orders.kitchen_display", "name": "orders.kitchen_display", "description": "Access kitchen display system", "category": "Orders", "isActive": true},

		// Menu Management
		{"id": "menu.view", "name": "menu.view", "description": "View menu items", "category": "Menu", "isActive": true},
		{"id": "menu.create", "name": "menu.create", "description": "Create new menu items", "category": "Menu", "isActive": true},
		{"id": "menu.edit", "name": "menu.edit", "description": "Edit menu items", "category": "Menu", "isActive": true},
		{"id": "menu.delete", "name": "menu.delete", "description": "Delete menu items", "category": "Menu", "isActive": true},
		{"id": "menu.manage_categories", "name": "menu.manage_categories", "description": "Manage menu categories", "category": "Menu", "isActive": true},
		{"id": "menu.manage_pricing", "name": "menu.manage_pricing", "description": "Manage menu pricing", "category": "Menu", "isActive": true},

		// Table & Reservation Management
		{"id": "tables.view", "name": "tables.view", "description": "View table layout and status", "category": "Tables", "isActive": true},
		{"id": "tables.manage", "name": "tables.manage", "description": "Manage table assignments", "category": "Tables", "isActive": true},
		{"id": "tables.configure", "name": "tables.configure", "description": "Configure table layout", "category": "Tables", "isActive": true},
		{"id": "reservations.view", "name": "reservations.view", "description": "View reservations", "category": "Reservations", "isActive": true},
		{"id": "reservations.create", "name": "reservations.create", "description": "Create new reservations", "category": "Reservations", "isActive": true},
		{"id": "reservations.edit", "name": "reservations.edit", "description": "Edit reservations", "category": "Reservations", "isActive": true},
		{"id": "reservations.cancel", "name": "reservations.cancel", "description": "Cancel reservations", "category": "Reservations", "isActive": true},

		// Inventory Management
		{"id": "inventory.view", "name": "inventory.view", "description": "View inventory levels", "category": "Inventory", "isActive": true},
		{"id": "inventory.manage", "name": "inventory.manage", "description": "Manage inventory items", "category": "Inventory", "isActive": true},
		{"id": "inventory.purchase_orders", "name": "inventory.purchase_orders", "description": "Manage purchase orders", "category": "Inventory", "isActive": true},
		{"id": "inventory.suppliers", "name": "inventory.suppliers", "description": "Manage suppliers", "category": "Inventory", "isActive": true},
		{"id": "inventory.stock_alerts", "name": "inventory.stock_alerts", "description": "Manage stock alerts", "category": "Inventory", "isActive": true},

		// Customer Management
		{"id": "customers.view", "name": "customers.view", "description": "View customer information", "category": "Customers", "isActive": true},
		{"id": "customers.manage", "name": "customers.manage", "description": "Manage customer profiles", "category": "Customers", "isActive": true},
		{"id": "customers.loyalty", "name": "customers.loyalty", "description": "Manage loyalty programs", "category": "Customers", "isActive": true},

		// Financial Management
		{"id": "payments.process", "name": "payments.process", "description": "Process payments", "category": "Payments", "isActive": true},
		{"id": "payments.refunds", "name": "payments.refunds", "description": "Process refunds", "category": "Payments", "isActive": true},
		{"id": "financial.view_reports", "name": "financial.view_reports", "description": "View financial reports", "category": "Financial", "isActive": true},
		{"id": "financial.manage_taxes", "name": "financial.manage_taxes", "description": "Manage tax settings", "category": "Financial", "isActive": true},

		// Settings & Configuration
		{"id": "settings.shop", "name": "settings.shop", "description": "Manage shop settings", "category": "Settings", "isActive": true},
		{"id": "settings.system", "name": "settings.system", "description": "Manage system settings", "category": "Settings", "isActive": true},
		{"id": "settings.integrations", "name": "settings.integrations", "description": "Manage integrations", "category": "Settings", "isActive": true},
	}

	permissions := map[string]interface{}{
		"data":        permissionsData,
		"total":       len(permissionsData),
		"page":        1,
		"limit":       len(permissionsData),
		"total_pages": 1,
	}

	return permissions, nil
}
