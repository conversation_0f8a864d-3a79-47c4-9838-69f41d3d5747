import { NextRequest, NextResponse } from 'next/server';
import { notificationService } from '@/services/notificationService';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * POST handler for marking all notifications as read
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Check authentication
    const { authenticated, user } = await checkAuth(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Mark all notifications as read
    const count = await notificationService.markAllAsRead(user.id);
    
    return NextResponse.json({ success: true, count });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/notifications/mark-all-read:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
