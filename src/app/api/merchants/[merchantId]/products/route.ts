import { NextRequest, NextResponse } from 'next/server';
import { inventoryService } from '@/services/inventoryService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for creating a product
const createProductSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  price: z.number().min(0, 'Price must be a non-negative number'),
  salePrice: z.number().min(0, 'Sale price must be a non-negative number').optional(),
  cost: z.number().min(0, 'Cost must be a non-negative number').optional(),
  categoryId: z.string().optional(),
  brandId: z.string().optional(),
  supplierId: z.string().optional(),
  taxRate: z.number().min(0, 'Tax rate must be a non-negative number').optional(),
  images: z.array(z.string()).optional(),
  attributes: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
  isService: z.boolean().default(false),
  inventoryTracking: z.boolean().default(true),
  stockQuantity: z.number().int().min(0, 'Stock quantity must be a non-negative integer').optional(),
  lowStockThreshold: z.number().int().min(0, 'Low stock threshold must be a non-negative integer').optional(),
});

/**
 * GET handler for fetching products
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId') || undefined;
    const includeInactive = searchParams.get('includeInactive') === 'true';
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get products
    const products = await inventoryService.getProducts(merchantId, categoryId, includeInactive);
    
    return NextResponse.json(products);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/products:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a product
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createProductSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the product
    const product = await inventoryService.createProduct({
      ...validationResult.data,
      merchantId,
    });
    
    return NextResponse.json(product, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/products:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
