import { NextRequest, NextResponse } from 'next/server';
import { campaignSegmentService } from '@/services';
import { supabase } from '@/lib/supabase/client';

/**
 * GET /api/merchants/[merchantId]/campaign-segments/[segmentId]/phones
 * Get phone numbers in a campaign segment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; segmentId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get phone numbers in segment
    const phones = await campaignSegmentService.getSegmentPhones(
      params.merchantId,
      params.segmentId
    );

    return NextResponse.json(phones);
  } catch (error) {
    console.error('Error fetching segment phone numbers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch segment phone numbers' },
      { status: 500 }
    );
  }
}
