import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    // Construct the backend URL (still using merchants route for compatibility)
    let url = `/merchants/${shopId}/branches/${branchId}/menu/items`;

    // Add any additional query parameters with defaults
    const otherParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      if (key !== 'shopId' && key !== 'branchId') {
        otherParams.append(key, value);
      }
    });

    // Add default pagination parameters if not provided
    if (!otherParams.has('page')) {
      otherParams.append('page', '1');
    }
    if (!otherParams.has('limit')) {
      otherParams.append('limit', '50');
    }

    if (otherParams.toString()) {
      url += `?${otherParams.toString()}`;
    }

    console.log('Fetching menu items from backend:', {
      fullUrl: url,
      shopId,
      branchId,
      queryParams: otherParams.toString()
    });

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/menu-items:', error);

    // Return sample data as fallback for development with proper pagination structure
    const sampleMenuItems = [
      {
        id: '1',
        slug: 'pad-thai',
        name: 'Pad Thai',
        category: 'Main Courses',
        description: 'Traditional Thai stir-fried noodles with shrimp, tofu, and vegetables',
        price: 12.99,
        image: 'https://images.unsplash.com/photo-1559314809-0f31657def5e?w=400',
        available: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        slug: 'green-curry',
        name: 'Green Curry',
        category: 'Main Courses',
        description: 'Spicy green curry with chicken, eggplant, and basil',
        price: 14.99,
        image: 'https://images.unsplash.com/photo-1455619452474-d2be8b1e70cd?w=400',
        available: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '3',
        slug: 'tom-yum-soup',
        name: 'Tom Yum Soup',
        category: 'Soups & Salads',
        description: 'Hot and sour soup with shrimp, mushrooms, and lemongrass',
        price: 8.99,
        image: 'https://images.unsplash.com/photo-1547592180-85f173990554?w=400',
        available: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    ];

    // Apply backend-style filtering and pagination to sample data
    const { searchParams } = new URL(request.url);
    const searchParam = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sort_by') || 'name';
    const sortOrder = searchParams.get('sort_order') || 'asc';

    let filteredItems = [...sampleMenuItems];

    // Apply search filter
    if (searchParam) {
      filteredItems = filteredItems.filter(item =>
        item.name.toLowerCase().includes(searchParam.toLowerCase()) ||
        item.description.toLowerCase().includes(searchParam.toLowerCase()) ||
        item.category.toLowerCase().includes(searchParam.toLowerCase())
      );
    }

    // Apply sorting
    filteredItems.sort((a, b) => {
      let aValue: any, bValue: any;
      switch (sortBy) {
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'created_at':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case 'updated_at':
          aValue = new Date(a.updatedAt);
          bValue = new Date(b.updatedAt);
          break;
        case 'category':
          aValue = a.category;
          bValue = b.category;
          break;
        default: // name
          aValue = a.name;
          bValue = b.name;
      }

      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const total = filteredItems.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = filteredItems.slice(startIndex, endIndex);

    // Return in backend response format
    const response = {
      data: paginatedItems,
      total,
      page,
      limit,
      total_pages: totalPages
    };

    return NextResponse.json(response);
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { shopId, branchId, category, image, ...menuItemData } = body;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    let url = `/merchants/${shopId}`;
    if (branchId) {
      url += `/branches/${branchId}`;
    }
    url += '/menu/items';

    // Transform the data to match backend expectations
    const backendData = {
      ...menuItemData,
      // Convert category string to category_id (for now, we'll create categories dynamically)
      // In a real app, you'd look up the category ID from a categories table
      category_id: null, // Let backend handle category creation
      // Convert single image to images array
      images: image ? [image] : [],
      // Set default values
      is_available: menuItemData.available !== false,
      is_vegetarian: false,
      is_vegan: false,
      is_gluten_free: false,
      is_spicy: false,
      spice_level: 0,
      preparation_time: 0,
      cooking_time: 0,
      ingredients: [],
      allergens: [],
      tags: category ? [category] : [],
    };

    console.log('Sending to backend:', { url, data: backendData });

    const response = await serverFetchClient(url, request, {
      method: 'POST',
      body: JSON.stringify(backendData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/menu-items:', error);

    // For development, create a mock response when backend fails
    const mockMenuItem = {
      id: Date.now().toString(),
      slug: menuItemData.name.toLowerCase().replace(/\s+/g, '-'),
      name: menuItemData.name,
      category: category || 'Main Courses',
      description: menuItemData.description || '',
      price: menuItemData.price,
      image: image || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400',
      available: menuItemData.available !== false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    console.log('Created mock menu item:', mockMenuItem);
    return NextResponse.json(mockMenuItem, { status: 201 });
  }
}
