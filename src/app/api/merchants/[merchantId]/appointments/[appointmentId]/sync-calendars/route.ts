import { NextRequest, NextResponse } from 'next/server';
import { calendarService } from '@/services/calendarService';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * POST handler for syncing an appointment with calendars
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; appointmentId: string } }
) {
  try {
    // Get the merchant ID and appointment ID from the URL
    const { merchantId, appointmentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Sync the appointment with calendars
    const result = await calendarService.syncAppointmentWithCalendars(merchantId, appointmentId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/appointments/[appointmentId]/sync-calendars:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
