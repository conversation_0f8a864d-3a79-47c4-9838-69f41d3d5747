import { NextRequest, NextResponse } from 'next/server';
import { appointmentCommunicationService } from '@/services';
import { z } from 'zod';
import { supabase } from '@/lib/supabase/client';

// Schema for sending appointment communications
const sendCommunicationSchema = z.object({
  type: z.enum(['confirmation', 'reminder', 'cancellation']),
  method: z.enum(['email', 'sms']),
});

/**
 * POST /api/merchants/[merchantId]/appointments/[appointmentId]/communications
 * Send a communication for an appointment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; appointmentId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = sendCommunicationSchema.parse(body);

    // Send the appropriate communication
    let communication;
    switch (validatedData.type) {
      case 'confirmation':
        communication = await appointmentCommunicationService.sendAppointmentConfirmation(
          params.merchantId,
          params.appointmentId,
          validatedData.method
        );
        break;
      case 'reminder':
        communication = await appointmentCommunicationService.sendAppointmentReminder(
          params.merchantId,
          params.appointmentId,
          validatedData.method
        );
        break;
      case 'cancellation':
        communication = await appointmentCommunicationService.sendAppointmentCancellation(
          params.merchantId,
          params.appointmentId,
          validatedData.method
        );
        break;
    }

    return NextResponse.json(communication, { status: 200 });
  } catch (error) {
    console.error('Error sending appointment communication:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to send appointment communication' },
      { status: 500 }
    );
  }
}


