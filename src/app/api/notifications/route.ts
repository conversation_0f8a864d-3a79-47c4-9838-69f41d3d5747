/**
 * Enhanced Notifications API Route with Backend-Driven Filtering, Sorting, and Pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';

export async function GET(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    // Validate required parameters
    if (!shopId || !branchId) {
      return NextResponse.json(
        { error: 'shopId and branchId are required' },
        { status: 400 }
      );
    }

    // Extract filtering, sorting, and pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sort_by = searchParams.get('sort_by') || 'timestamp';
    const sort_order = searchParams.get('sort_order') || 'desc';

    // Filtering parameters
    const type = searchParams.get('type');
    const priority = searchParams.get('priority');
    const isRead = searchParams.get('isRead');
    const search = searchParams.get('search');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const dateRange = searchParams.get('dateRange');

    // Build query parameters for backend
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sort_by,
      sort_order,
    });

    // Add optional filters
    if (type) queryParams.append('type', type);
    if (priority) queryParams.append('priority', priority);
    if (isRead !== null) queryParams.append('is_read', isRead || 'false');
    if (search) queryParams.append('search', search);
    if (startDate) queryParams.append('start_date', startDate);
    if (endDate) queryParams.append('end_date', endDate);
    if (dateRange) queryParams.append('date_range', dateRange);

    // Construct backend URL
    const backendUrl = `/shops/${shopId}/branches/${branchId}/notifications?${queryParams.toString()}`;

    console.log('Fetching notifications from backend:', {
      fullUrl: backendUrl,
      shopId,
      branchId,
      queryParams: queryParams.toString()
    });

    // Make request to backend
    const response = await serverFetchClient(backendUrl, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend notifications API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });

      return NextResponse.json(
        { error: `Backend API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Transform backend response to match frontend expectations
    const transformedResponse = {
      data: data.notifications || [],
      total: data.total || 0,
      page: data.page || page,
      limit: data.limit || limit,
      totalPages: data.totalPages || Math.ceil((data.total || 0) / limit),
      summary: {
        totalNotifications: data.summary?.total || 0,
        unreadNotifications: data.summary?.unread || 0,
        readNotifications: data.summary?.read || 0,
        urgentNotifications: data.summary?.urgent || 0,
        highPriorityNotifications: data.summary?.highPriority || 0,
        byType: data.summary?.byType || {},
        byPriority: data.summary?.byPriority || {},
      },
    };

    return NextResponse.json(transformedResponse);

  } catch (error) {
    console.error('Notifications API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    // Validate required parameters
    if (!shopId || !branchId) {
      return NextResponse.json(
        { error: 'shopId and branchId are required' },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { title, message, type, priority, link, actionLabel, data, userId } = body;

    // Validate required fields
    if (!title || !message || !type) {
      return NextResponse.json(
        { error: 'title, message, and type are required' },
        { status: 400 }
      );
    }

    // Construct backend URL
    const backendUrl = `/shops/${shopId}/branches/${branchId}/notifications`;

    console.log('Creating notification via backend:', {
      fullUrl: backendUrl,
      shopId,
      branchId,
      notificationData: { title, message, type, priority }
    });

    // Make request to backend
    const response = await serverFetchClient(backendUrl, request, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title,
        message,
        type,
        priority: priority || 'medium',
        link,
        actionLabel,
        data,
        userId,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend create notification API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });

      return NextResponse.json(
        { error: `Backend API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data, { status: 201 });

  } catch (error) {
    console.error('Create notification API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
