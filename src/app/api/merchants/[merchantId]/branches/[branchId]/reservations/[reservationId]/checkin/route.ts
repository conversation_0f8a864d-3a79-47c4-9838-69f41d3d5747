/**
 * API route for reservation check-in
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reservationId: string }> }
) {
  try {
    const { merchantId, branchId, reservationId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}/checkin`, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/branches/[branchId]/reservations/[reservationId]/checkin:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to check in reservation' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
