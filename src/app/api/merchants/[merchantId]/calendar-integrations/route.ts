import { NextRequest, NextResponse } from 'next/server';
import { calendarService } from '@/services/calendarService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for calendar integration
const calendarIntegrationSchema = z.object({
  provider: z.enum(['google', 'outlook', 'apple', 'custom']),
  name: z.string().min(1, 'Name is required'),
  calendarId: z.string().min(1, 'Calendar ID is required'),
  accessToken: z.string().min(1, 'Access token is required'),
  refreshToken: z.string().optional(),
  tokenExpiry: z.string().optional().transform(val => val ? new Date(val) : undefined),
  settings: z.object({
    syncEnabled: z.boolean().default(true),
    syncDirection: z.enum(['import', 'export', 'both']).default('both'),
    colorId: z.string().optional(),
    defaultReminders: z.array(
      z.object({
        method: z.string(),
        minutes: z.number().int(),
      })
    ).optional(),
  }).default({
    syncEnabled: true,
    syncDirection: 'both',
  }),
  userId: z.string().optional(),
});

/**
 * GET handler for fetching calendar integrations
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get calendar integrations
    const integrations = await calendarService.getCalendarIntegrations(merchantId);
    
    return NextResponse.json(integrations);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/calendar-integrations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a calendar integration
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated, user } = await checkAuth(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = calendarIntegrationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the calendar integration
    const integration = await calendarService.createCalendarIntegration({
      ...validationResult.data,
      merchantId,
      userId: validationResult.data.userId || user.id,
    });
    
    return NextResponse.json(integration, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/calendar-integrations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
