import { NextRequest, NextResponse } from 'next/server';
import { checkAuth } from '@/lib/auth/checkAuth';
import { menuItemSchema } from '@/lib/validations/menuItemSchema';

// Reference to the mock database from the parent route
// In a real app, this would be a database connection
let menuItems: Record<string, any[]> = {};

/**
 * GET handler for fetching a specific menu item
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; itemId: string } }
) {
  try {
    // Get the merchant ID and item ID from the URL
    const { merchantId, itemId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the merchant's menu items
    const items = menuItems[merchantId] || [];
    
    // Find the specific item
    const item = items.find(item => item.id === itemId);
    
    if (!item) {
      return NextResponse.json(
        { error: 'Menu item not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(item);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/menu-items/[itemId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a menu item
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; itemId: string } }
) {
  try {
    // Get the merchant ID and item ID from the URL
    const { merchantId, itemId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = menuItemSchema.partial().safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Get the merchant's menu items
    const items = menuItems[merchantId] || [];
    
    // Find the index of the item to update
    const itemIndex = items.findIndex(item => item.id === itemId);
    
    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'Menu item not found' },
        { status: 404 }
      );
    }
    
    // Update the item
    const updatedItem = {
      ...items[itemIndex],
      ...validationResult.data,
      updatedAt: new Date().toISOString(),
    };
    
    // Replace the old item with the updated one
    items[itemIndex] = updatedItem;
    
    return NextResponse.json(updatedItem);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/menu-items/[itemId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a menu item
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; itemId: string } }
) {
  try {
    // Get the merchant ID and item ID from the URL
    const { merchantId, itemId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the merchant's menu items
    const items = menuItems[merchantId] || [];
    
    // Find the index of the item to delete
    const itemIndex = items.findIndex(item => item.id === itemId);
    
    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'Menu item not found' },
        { status: 404 }
      );
    }
    
    // Remove the item
    items.splice(itemIndex, 1);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/menu-items/[itemId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
