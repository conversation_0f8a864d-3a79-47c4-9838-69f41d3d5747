import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const merchantId = searchParams.get('merchantId');
    const category = searchParams.get('category');

    if (!merchantId) {
      return NextResponse.json(
        { error: 'merchantId is required' },
        { status: 400 }
      );
    }

    // Build backend URL
    let url = `/merchants/${merchantId}/convenience/products`;
    const params = new URLSearchParams();
    if (category) params.append('category', category);

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/convenience-products:', error);

    // Return error response - no mock data fallback
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch convenience products' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { merchantId, ...productData } = body;

    if (!merchantId) {
      return NextResponse.json(
        { error: 'merchantId is required' },
        { status: 400 }
      );
    }

    const response = await serverFetchClient(`/merchants/${merchantId}/convenience/products`, request, {
      method: 'POST',
      body: JSON.stringify(productData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/convenience-products:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create convenience product' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
