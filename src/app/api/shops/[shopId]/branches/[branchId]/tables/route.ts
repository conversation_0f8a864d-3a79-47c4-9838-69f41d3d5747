/**
 * API route for shop branch tables management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/shops/${shopId}/branches/${branchId}/tables${queryString ? `?${queryString}` : ''}`;

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error response:', errorText);
      throw new Error(`Backend responded with status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Successfully fetched tables:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/shops/[shopId]/branches/[branchId]/tables:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch tables' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    const body = await request.json();

    console.log('Creating table with data:', JSON.stringify(body, null, 2));

    const response = await serverFetchClient(`/shops/${shopId}/branches/${branchId}/tables`, request, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error response:', errorText);
      throw new Error(`Backend responded with status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Successfully created table:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/shops/[shopId]/branches/[branchId]/tables:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create table' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
