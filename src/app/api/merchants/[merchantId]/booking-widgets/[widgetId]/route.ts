import { NextRequest, NextResponse } from 'next/server';
import { bookingWidgetService } from '@/services/bookingWidgetService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a booking widget
const updateBookingWidgetSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  allowedOrigins: z.array(z.string()).optional(),
  settings: z.object({
    theme: z.object({
      primaryColor: z.string().optional(),
      secondaryColor: z.string().optional(),
      fontFamily: z.string().optional(),
      borderRadius: z.string().optional(),
      buttonStyle: z.string().optional(),
    }).optional(),
    layout: z.object({
      showHeader: z.boolean().optional(),
      showFooter: z.boolean().optional(),
      showLogo: z.boolean().optional(),
      logoUrl: z.string().optional(),
      customCss: z.string().optional(),
    }).optional(),
    booking: z.object({
      requireCustomerDetails: z.boolean().optional(),
      requireCustomerPhone: z.boolean().optional(),
      requireCustomerEmail: z.boolean().optional(),
      allowGuestBooking: z.boolean().optional(),
      showPrices: z.boolean().optional(),
      showDuration: z.boolean().optional(),
      showAvailableStaff: z.boolean().optional(),
      enableServiceFiltering: z.boolean().optional(),
      enableStaffSelection: z.boolean().optional(),
      maxDaysInAdvance: z.number().int().min(1).optional(),
      minHoursInAdvance: z.number().int().min(0).optional(),
      timeSlotInterval: z.number().int().min(5).optional(),
    }).optional(),
    services: z.object({
      includedServiceIds: z.array(z.string()).optional(),
      excludedServiceIds: z.array(z.string()).optional(),
      includedCategoryIds: z.array(z.string()).optional(),
      excludedCategoryIds: z.array(z.string()).optional(),
    }).optional(),
    staff: z.object({
      includedStaffIds: z.array(z.string()).optional(),
      excludedStaffIds: z.array(z.string()).optional(),
    }).optional(),
  }).optional(),
  isActive: z.boolean().optional(),
});

/**
 * GET handler for fetching a specific booking widget
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; widgetId: string } }
) {
  try {
    // Get the merchant ID and widget ID from the URL
    const { merchantId, widgetId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the booking widget
    const widget = await bookingWidgetService.getBookingWidgetById(merchantId, widgetId);
    
    return NextResponse.json(widget);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/booking-widgets/[widgetId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a booking widget
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; widgetId: string } }
) {
  try {
    // Get the merchant ID and widget ID from the URL
    const { merchantId, widgetId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateBookingWidgetSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the booking widget
    const widget = await bookingWidgetService.updateBookingWidget(
      merchantId,
      widgetId,
      validationResult.data
    );
    
    return NextResponse.json(widget);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/booking-widgets/[widgetId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a booking widget
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; widgetId: string } }
) {
  try {
    // Get the merchant ID and widget ID from the URL
    const { merchantId, widgetId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the booking widget
    const result = await bookingWidgetService.deleteBookingWidget(merchantId, widgetId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/booking-widgets/[widgetId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
