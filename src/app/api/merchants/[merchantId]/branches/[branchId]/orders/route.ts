/**
 * API route for orders
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/orders${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching orders:', error);
    
    // Return mock data for development
    const mockData = [
      {
        id: '1',
        merchantId: 'merchant-1',
        customerId: 'customer-1',
        customer: {
          name: '<PERSON> Doe',
          email: '<EMAIL>',
          phone: '+1234567890'
        },
        items: [
          {
            id: '1',
            menuItemId: 'item-1',
            name: 'Chicken Sandwich',
            price: 12.99,
            quantity: 2,
            options: []
          }
        ],
        status: 'pending',
        total: 25.98,
        subtotal: 25.98,
        tax: 2.08,
        tip: 0,
        discount: 0,
        paymentMethod: 'credit_card',
        paymentStatus: 'pending',
        orderType: 'dine-in',
        tableId: 'table-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        merchantId: 'merchant-1',
        customerId: 'customer-2',
        customer: {
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '+1234567891'
        },
        items: [
          {
            id: '2',
            menuItemId: 'item-2',
            name: 'Caesar Salad',
            price: 9.99,
            quantity: 1,
            options: []
          }
        ],
        status: 'preparing',
        total: 9.99,
        subtotal: 9.99,
        tax: 0.80,
        tip: 2.00,
        discount: 0,
        paymentMethod: 'cash',
        paymentStatus: 'paid',
        orderType: 'takeout',
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 15 * 60 * 1000).toISOString()
      }
    ];
    
    return NextResponse.json(mockData);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating order:', error);
    
    // Return mock created order for development
    const mockOrder = {
      id: `order-${Date.now()}`,
      merchantId: 'merchant-1',
      customerId: 'customer-new',
      customer: {
        name: 'New Customer',
        email: '<EMAIL>',
        phone: '+1234567892'
      },
      items: [],
      status: 'pending',
      total: 0,
      subtotal: 0,
      tax: 0,
      tip: 0,
      discount: 0,
      paymentMethod: 'credit_card',
      paymentStatus: 'pending',
      orderType: 'dine-in',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return NextResponse.json(mockOrder);
  }
}
