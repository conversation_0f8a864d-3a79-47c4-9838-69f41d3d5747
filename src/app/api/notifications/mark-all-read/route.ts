import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Mock data - in a real app, this would be in a database
let mockNotifications = [
  {
    id: '1',
    type: 'order',
    title: 'New Order Received',
    message: 'Order #1234 from <PERSON> - $45.99',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    priority: 'high',
    actionUrl: '/app/restaurant/orders/1234',
    actionLabel: 'View Order',
    metadata: { orderId: '1234', amount: 45.99 }
  },
  {
    id: '2',
    type: 'reservation',
    title: 'New Reservation',
    message: 'Table for 4 at 7:00 PM today - <PERSON>',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    read: false,
    priority: 'medium',
    actionUrl: '/app/restaurant/reservations',
    actionLabel: 'View Reservation'
  },
  // ... other notifications
];

export async function PATCH(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Mark all notifications as read
    const updatedCount = mockNotifications.filter(n => !n.read).length;
    mockNotifications = mockNotifications.map(notification => ({
      ...notification,
      read: true
    }));

    // In a real app, update database
    // const result = await db.notification.updateMany({
    //   where: { 
    //     userId: session.user.id,
    //     read: false 
    //   },
    //   data: { 
    //     read: true,
    //     readAt: new Date()
    //   }
    // });

    return NextResponse.json({
      message: `${updatedCount} notifications marked as read`,
      updatedCount,
      notifications: mockNotifications
    });

  } catch (error) {
    console.error('Mark all as read error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
