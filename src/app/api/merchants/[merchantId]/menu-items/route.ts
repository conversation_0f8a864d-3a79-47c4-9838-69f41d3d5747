import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { checkAuth } from '@/lib/auth/checkAuth';
import { menuItemSchema } from '@/lib/validations/menuItemSchema';

// Mock database for menu items
let menuItems: Record<string, any[]> = {};

/**
 * GET handler for fetching menu items
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get menu items for the merchant
    const items = menuItems[merchantId] || [];
    
    return NextResponse.json(items);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/menu-items:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a menu item
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = menuItemSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create a new menu item
    const newItem = {
      id: uuidv4(),
      merchantId,
      ...validationResult.data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Initialize the merchant's menu items array if it doesn't exist
    if (!menuItems[merchantId]) {
      menuItems[merchantId] = [];
    }
    
    // Add the new item to the merchant's menu items
    menuItems[merchantId].push(newItem);
    
    return NextResponse.json(newItem, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/menu-items:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
