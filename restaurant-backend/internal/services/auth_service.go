package services

import (
	"context"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// AuthService handles authentication business logic
type AuthService struct {
	userRepo  repositories.UserRepository
	secretKey string
	expiresIn time.Duration
}

// NewAuthService creates a new auth service
func NewAuthService(userRepo repositories.UserRepository, secretKey string, expiresIn time.Duration) *AuthService {
	return &AuthService{
		userRepo:  userRepo,
		secretKey: secretKey,
		expiresIn: expiresIn,
	}
}

// Login authenticates a user and returns a JWT token
func (s *AuthService) Login(ctx context.Context, req types.LoginRequest) (*types.LoginResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Find user by email
	// 2. Verify password
	// 3. Generate JWT token
	// 4. Return response

	// For now, return a mock response
	user := &models.User{
		BaseModel: models.BaseModel{ID: uuid.New()},
		Email:     req.Email,
		FirstName: "Demo",
		LastName:  "User",
	}

	token, err := s.generateToken(user)
	if err != nil {
		return nil, err
	}

	return &types.LoginResponse{
		Token:     token,
		User:      user,
		ExpiresAt: time.Now().Add(s.expiresIn),
	}, nil
}

// Register creates a new user account
func (s *AuthService) Register(ctx context.Context, req types.RegisterRequest) (*types.RegisterResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Check if user already exists
	// 2. Hash password
	// 3. Create user in database
	// 4. Return response

	user := &models.User{
		BaseModel: models.BaseModel{ID: uuid.New()},
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Phone:     req.Phone,
	}

	return &types.RegisterResponse{
		User:    user,
		Message: "User registered successfully",
	}, nil
}

// RefreshToken refreshes an expired JWT token
func (s *AuthService) RefreshToken(ctx context.Context, req types.RefreshTokenRequest) (*types.LoginResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Validate refresh token
	// 2. Get user from refresh token
	// 3. Generate new JWT token
	// 4. Return response

	return &types.LoginResponse{
		Token:     "new-jwt-token",
		ExpiresAt: time.Now().Add(s.expiresIn),
	}, nil
}

// GetCurrentUser returns the current authenticated user
func (s *AuthService) GetCurrentUser(ctx context.Context, userID interface{}) (*models.User, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Get user ID from context
	// 2. Fetch user from database
	// 3. Return user

	var uid uuid.UUID

	// Handle both string (NextAuth) and UUID (legacy JWT) formats
	switch v := userID.(type) {
	case string:
		// Try to parse as UUID for database operations
		if parsedUUID, err := uuid.Parse(v); err == nil {
			uid = parsedUUID
		} else {
			// For OAuth provider IDs, generate a deterministic UUID
			// In a real implementation, you'd have a user mapping table
			uid = uuid.New() // This should be replaced with proper user lookup
		}
	case uuid.UUID:
		uid = v
	default:
		return nil, fmt.Errorf("invalid user ID format")
	}

	user := &models.User{
		BaseModel: models.BaseModel{ID: uid},
		Email:     "<EMAIL>",
		FirstName: "Demo",
		LastName:  "User",
	}

	return user, nil
}

// generateToken generates a JWT token for a user
func (s *AuthService) generateToken(user *models.User) (string, error) {
	// Handle nil BranchID by using a zero UUID
	var branchID uuid.UUID
	if user.BranchID != nil {
		branchID = *user.BranchID
	}

	claims := &types.Claims{
		UserID:     user.ID,
		MerchantID: user.MerchantID,
		BranchID:   branchID,
		Role:       user.Role.Name,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.expiresIn)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "restaurant-api",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.secretKey))
}
