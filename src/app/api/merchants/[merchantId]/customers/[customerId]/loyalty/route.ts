import { NextRequest, NextResponse } from 'next/server';
import { loyaltyService } from '@/services/loyaltyService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for adding loyalty points
const addLoyaltyPointsSchema = z.object({
  amount: z.number().min(0, 'Amount must be a non-negative number'),
  appointmentId: z.string().optional(),
  description: z.string().optional(),
});

// Schema for redeeming loyalty points
const redeemLoyaltyPointsSchema = z.object({
  points: z.number().int().positive('Points must be a positive integer'),
  description: z.string().optional(),
});

/**
 * GET handler for fetching customer loyalty points
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; customerId: string } }
) {
  try {
    // Get the merchant ID and customer ID from the URL
    const { merchantId, customerId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get customer loyalty points
    const result = await loyaltyService.getCustomerLoyaltyPoints(merchantId, customerId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/customers/[customerId]/loyalty:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for adding loyalty points
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; customerId: string } }
) {
  try {
    // Get the merchant ID and customer ID from the URL
    const { merchantId, customerId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Check if this is an add or redeem operation
    const isRedeem = body.points !== undefined;
    
    if (isRedeem) {
      // Validate the request body for redeeming points
      const validationResult = redeemLoyaltyPointsSchema.safeParse(body);
      
      if (!validationResult.success) {
        return NextResponse.json(
          { error: 'Invalid request data', details: validationResult.error.format() },
          { status: 400 }
        );
      }
      
      // Redeem loyalty points
      const { points, description } = validationResult.data;
      const result = await loyaltyService.redeemLoyaltyPoints(
        merchantId,
        customerId,
        points,
        description
      );
      
      return NextResponse.json(result);
    } else {
      // Validate the request body for adding points
      const validationResult = addLoyaltyPointsSchema.safeParse(body);
      
      if (!validationResult.success) {
        return NextResponse.json(
          { error: 'Invalid request data', details: validationResult.error.format() },
          { status: 400 }
        );
      }
      
      // Add loyalty points
      const { amount, appointmentId, description } = validationResult.data;
      const result = await loyaltyService.addLoyaltyPoints(
        merchantId,
        customerId,
        amount,
        appointmentId,
        description
      );
      
      return NextResponse.json(result);
    }
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/customers/[customerId]/loyalty:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
