/**
 * API route for real-time metrics
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/metrics/realtime`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching real-time metrics:', error);
    
    // Return mock data for development with some randomization to simulate real-time updates
    const mockData = {
      activeOrders: Math.floor(Math.random() * 20) + 5,
      todayRevenue: Math.floor(Math.random() * 2000) + 800,
      onlineCustomers: Math.floor(Math.random() * 50) + 10,
      averageWaitTime: Math.floor(Math.random() * 15) + 5
    };
    
    return NextResponse.json(mockData);
  }
}
