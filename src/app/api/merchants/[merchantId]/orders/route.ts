import { NextRequest, NextResponse } from 'next/server';
import { checkAuth } from '@/lib/auth';

// Mock orders data
export const orders = {
  'current-merchant-id': [
    {
      id: '#12345',
      merchantId: 'current-merchant-id',
      customerId: 'customer-1',
      customer: {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+1234567890',
      },
      items: [
        {
          id: 'item-1',
          menuItemId: 'menu-1',
          name: 'Margherita Pizza',
          price: 12.99,
          quantity: 2,
          options: [],
        },
        {
          id: 'item-2',
          menuItemId: 'menu-2',
          name: 'Caesar Salad',
          price: 8.99,
          quantity: 1,
          options: [],
        },
      ],
      status: 'preparing',
      total: 34.97,
      subtotal: 34.97,
      tax: 0,
      tip: 0,
      discount: 0,
      paymentMethod: 'credit_card',
      paymentStatus: 'paid',
      orderType: 'dine-in',
      tableId: 'table-1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '#12346',
      merchantId: 'current-merchant-id',
      customerId: 'customer-2',
      customer: {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+1234567891',
      },
      items: [
        {
          id: 'item-3',
          menuItemId: 'menu-3',
          name: 'Pasta Carbonara',
          price: 14.99,
          quantity: 1,
          options: [],
        },
        {
          id: 'item-4',
          menuItemId: 'menu-4',
          name: 'Garlic Bread',
          price: 4.99,
          quantity: 1,
          options: [],
        },
      ],
      status: 'preparing',
      total: 19.98,
      subtotal: 19.98,
      tax: 0,
      tip: 0,
      discount: 0,
      paymentMethod: 'cash',
      paymentStatus: 'paid',
      orderType: 'dine-in',
      tableId: 'table-2',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '#12347',
      merchantId: 'current-merchant-id',
      customerId: 'customer-3',
      customer: {
        name: 'Noah Thompson',
        email: '<EMAIL>',
        phone: '+1234567892',
      },
      items: [
        {
          id: 'item-5',
          menuItemId: 'menu-5',
          name: 'Chicken Sandwich',
          price: 10.99,
          quantity: 1,
          options: [],
        },
        {
          id: 'item-6',
          menuItemId: 'menu-6',
          name: 'Fries',
          price: 3.99,
          quantity: 1,
          options: [],
        },
      ],
      status: 'preparing',
      total: 14.98,
      subtotal: 14.98,
      tax: 0,
      tip: 0,
      discount: 0,
      paymentMethod: 'credit_card',
      paymentStatus: 'paid',
      orderType: 'takeout',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '#12344',
      merchantId: 'current-merchant-id',
      customerId: 'customer-4',
      customer: {
        name: 'Sophia Davis',
        email: '<EMAIL>',
        phone: '+1234567893',
      },
      items: [
        {
          id: 'item-7',
          menuItemId: 'menu-7',
          name: 'Burger',
          price: 11.99,
          quantity: 1,
          options: [],
        },
        {
          id: 'item-8',
          menuItemId: 'menu-8',
          name: 'Onion Rings',
          price: 4.99,
          quantity: 1,
          options: [],
        },
      ],
      status: 'completed',
      total: 16.98,
      subtotal: 16.98,
      tax: 0,
      tip: 0,
      discount: 0,
      paymentMethod: 'credit_card',
      paymentStatus: 'paid',
      orderType: 'dine-in',
      tableId: 'table-3',
      createdAt: new Date(Date.now() - 20 * 60000).toISOString(),
      updatedAt: new Date(Date.now() - 10 * 60000).toISOString(),
      completedAt: new Date(Date.now() - 10 * 60000).toISOString(),
    },
    {
      id: '#12343',
      merchantId: 'current-merchant-id',
      customerId: 'customer-5',
      customer: {
        name: 'Liam Wilson',
        email: '<EMAIL>',
        phone: '+1234567894',
      },
      items: [
        {
          id: 'item-9',
          menuItemId: 'menu-9',
          name: 'Steak',
          price: 24.99,
          quantity: 1,
          options: [],
        },
        {
          id: 'item-10',
          menuItemId: 'menu-10',
          name: 'Mashed Potatoes',
          price: 5.99,
          quantity: 1,
          options: [],
        },
      ],
      status: 'completed',
      total: 30.98,
      subtotal: 30.98,
      tax: 0,
      tip: 0,
      discount: 0,
      paymentMethod: 'credit_card',
      paymentStatus: 'paid',
      orderType: 'dine-in',
      tableId: 'table-4',
      createdAt: new Date(Date.now() - 35 * 60000).toISOString(),
      updatedAt: new Date(Date.now() - 15 * 60000).toISOString(),
      completedAt: new Date(Date.now() - 15 * 60000).toISOString(),
    },
  ],
};

/**
 * GET handler for fetching orders
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const merchantId = params.merchantId;

    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');

    // Get orders for the merchant
    const merchantOrders = orders[merchantId] || [];

    // Filter by status if provided
    const filteredOrders = status
      ? merchantOrders.filter(order => order.status === status)
      : merchantOrders;

    return NextResponse.json(filteredOrders);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/orders:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
