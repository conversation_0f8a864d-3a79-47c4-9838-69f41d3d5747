import { NextRequest, NextResponse } from 'next/server';
import { loyaltyService } from '@/services/loyaltyService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a loyalty tier
const updateLoyaltyTierSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  requiredPoints: z.number().int().min(0, 'Required points must be a non-negative integer').optional(),
  benefits: z.array(z.string()).optional(),
  multiplier: z.number().min(1, 'Multiplier must be at least 1').optional(),
});

/**
 * PUT handler for updating a loyalty tier
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; programId: string; tierId: string } }
) {
  try {
    // Get the merchant ID, program ID, and tier ID from the URL
    const { merchantId, programId, tierId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateLoyaltyTierSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the loyalty tier
    const tier = await loyaltyService.updateLoyaltyTier(
      merchantId,
      programId,
      tierId,
      validationResult.data
    );
    
    return NextResponse.json(tier);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/loyalty-programs/[programId]/tiers/[tierId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a loyalty tier
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; programId: string; tierId: string } }
) {
  try {
    // Get the merchant ID, program ID, and tier ID from the URL
    const { merchantId, programId, tierId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the loyalty tier
    const result = await loyaltyService.deleteLoyaltyTier(merchantId, programId, tierId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/loyalty-programs/[programId]/tiers/[tierId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
