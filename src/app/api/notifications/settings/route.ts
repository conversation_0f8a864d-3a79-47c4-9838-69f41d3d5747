import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Mock settings - in a real app, this would be in a database
let mockSettings = {
  emailNotifications: true,
  pushNotifications: true,
  smsNotifications: false,
  soundEnabled: true,
  doNotDisturb: {
    enabled: false,
    startTime: '22:00',
    endTime: '08:00',
  },
  emailDigest: {
    enabled: true,
    frequency: 'daily',
  },
  categories: {
    orders: {
      enabled: true,
      channels: { email: true, push: true, sms: false, inApp: true },
      priority: 'high'
    },
    reservations: {
      enabled: true,
      channels: { email: true, push: true, sms: false, inApp: true },
      priority: 'medium'
    },
    reviews: {
      enabled: true,
      channels: { email: true, push: false, sms: false, inApp: true },
      priority: 'low'
    },
    inventory: {
      enabled: true,
      channels: { email: true, push: true, sms: true, inApp: true },
      priority: 'high'
    },
    staff: {
      enabled: true,
      channels: { email: true, push: false, sms: false, inApp: true },
      priority: 'medium'
    },
    system: {
      enabled: true,
      channels: { email: true, push: false, sms: false, inApp: true },
      priority: 'low'
    },
    marketing: {
      enabled: false,
      channels: { email: true, push: false, sms: false, inApp: false },
      priority: 'low'
    }
  }
};

export async function GET(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // In a real app, fetch from database
    // const settings = await db.notificationSettings.findUnique({
    //   where: { userId: session.user.id }
    // });

    return NextResponse.json({
      settings: mockSettings
    });

  } catch (error) {
    console.error('Get notification settings error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate settings
    const allowedFields = [
      'emailNotifications',
      'pushNotifications', 
      'smsNotifications',
      'soundEnabled',
      'doNotDisturb',
      'emailDigest',
      'categories'
    ];

    const updates: any = {};
    for (const [key, value] of Object.entries(body)) {
      if (allowedFields.includes(key)) {
        updates[key] = value;
      }
    }

    // Update mock settings
    mockSettings = { ...mockSettings, ...updates };

    // In a real app, update database
    // const updatedSettings = await db.notificationSettings.upsert({
    //   where: { userId: session.user.id },
    //   update: updates,
    //   create: { userId: session.user.id, ...updates }
    // });

    return NextResponse.json({
      message: 'Notification settings updated successfully',
      settings: mockSettings
    });

  } catch (error) {
    console.error('Update notification settings error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
