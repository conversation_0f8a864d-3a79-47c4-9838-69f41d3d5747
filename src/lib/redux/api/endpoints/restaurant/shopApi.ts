/**
 * RTK Query API endpoints for shop management
 * Handles all shop-related API calls (replaces merchantApi)
 */

import { apiSlice } from '../../apiSlice';

export interface ShopBranch {
  id: string;
  name: string;
  slug: string;
  address?: string;
  phone?: string;
  email?: string;
  status: string;
}

export interface Shop {
  id: string;
  name: string;
  slug: string;
  description?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  logo?: string;
  coverImage?: string;
  cuisine: string;
  priceRange: string;
  rating: number;
  reviewCount: number;
  isActive: boolean;
  openingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  location: {
    latitude: number;
    longitude: number;
  };
  features: string[];
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  branches?: ShopBranch[];
  createdAt: string;
  updatedAt: string;
}

export interface ShopsResponse {
  data: Shop[];
  total: number;
  page: number;
  limit: number;
}

export interface CreateShopRequest {
  name: string;
  slug: string;
  description?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  cuisine: string;
  priceRange: string;
  openingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  location: {
    latitude: number;
    longitude: number;
  };
  features?: string[];
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
}

export interface UpdateShopRequest extends Partial<CreateShopRequest> {
  id: string;
}

export const shopApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all shops
    getShops: builder.query<ShopsResponse, {
      page?: number;
      limit?: number;
      search?: string;
      cuisine?: string;
      priceRange?: string;
      isActive?: boolean;
    }>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            searchParams.append(key, String(value));
          }
        });

        return {
          url: `/shops?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: ['Shop'],
    }),

    // Get shop by ID
    getShop: builder.query<Shop, string>({
      query: (id) => ({
        url: `/shops/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Shop', id }],
    }),

    // Get shop by slug
    getShopBySlug: builder.query<Shop, string>({
      query: (slug) => ({
        url: `/shops/slug/${slug}`,
        method: 'GET',
      }),
      providesTags: (result, error, slug) => [{ type: 'Shop', id: slug }],
    }),

    // Create new shop
    createShop: builder.mutation<Shop, CreateShopRequest>({
      query: (shopData) => ({
        url: '/shops',
        method: 'POST',
        body: shopData,
      }),
      invalidatesTags: ['Shop'],
    }),

    // Update shop
    updateShop: builder.mutation<Shop, UpdateShopRequest>({
      query: ({ id, ...shopData }) => ({
        url: `/shops/${id}`,
        method: 'PUT',
        body: shopData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Shop', id },
        'Shop',
      ],
    }),

    // Delete shop
    deleteShop: builder.mutation<void, string>({
      query: (id) => ({
        url: `/shops/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Shop'],
    }),

    // Get shops by owner
    getShopsByOwner: builder.query<ShopsResponse, string>({
      query: (ownerId) => ({
        url: `/shops/owner/${ownerId}`,
        method: 'GET',
      }),
      providesTags: ['Shop'],
    }),

    // Get shops by type/cuisine
    getShopsByType: builder.query<ShopsResponse, string>({
      query: (type) => ({
        url: `/shops/type/${type}`,
        method: 'GET',
      }),
      providesTags: ['Shop'],
    }),
  }),
});

export const {
  useGetShopsQuery,
  useGetShopQuery,
  useGetShopBySlugQuery,
  useCreateShopMutation,
  useUpdateShopMutation,
  useDeleteShopMutation,
  useGetShopsByOwnerQuery,
  useGetShopsByTypeQuery,
} = shopApi;

// Legacy exports for backward compatibility
export const merchantApi = shopApi;
export const {
  useGetShopsQuery: useGetMerchantsQuery,
  useGetShopQuery: useGetMerchantQuery,
  useGetShopBySlugQuery: useGetMerchantBySlugQuery,
  useCreateShopMutation: useCreateMerchantMutation,
  useUpdateShopMutation: useUpdateMerchantMutation,
  useDeleteShopMutation: useDeleteMerchantMutation,
} = shopApi;

// Legacy type exports
export type Merchant = Shop;
export type MerchantFilters = { page?: number; limit?: number; search?: string; cuisine?: string; priceRange?: string; isActive?: boolean; };
export type CreateMerchantRequest = CreateShopRequest;
export type UpdateMerchantRequest = UpdateShopRequest;
export type MerchantsResponse = ShopsResponse;
