import { NextRequest, NextResponse } from 'next/server';
import { voucherService } from '@/services/voucherService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for generating a gift card
const generateGiftCardSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  recipientEmail: z.string().email('Invalid recipient email'),
  recipientName: z.string().min(1, 'Recipient name is required'),
  senderName: z.string().min(1, 'Sender name is required'),
  message: z.string().optional(),
});

/**
 * GET handler for fetching gift cards
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get vouchers that are gift cards
    const vouchers = await prisma.voucher.findMany({
      where: {
        merchantId,
        isGiftCard: true,
      },
      include: {
        usages: {
          include: {
            appointment: {
              select: {
                id: true,
                date: true,
                startTime: true,
                customerName: true,
              },
            },
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
    
    return NextResponse.json(vouchers);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/gift-cards:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for generating a gift card
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = generateGiftCardSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Generate the gift card
    const { amount, recipientEmail, recipientName, senderName, message } = validationResult.data;
    const giftCard = await voucherService.generateGiftCard(
      merchantId,
      amount,
      recipientEmail,
      recipientName,
      senderName,
      message
    );
    
    return NextResponse.json(giftCard, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/gift-cards:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
