/**
 * API route for responding to reviews
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reviewId: string }> }
) {
  try {
    const { merchantId, branchId, reviewId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/respond`, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/branches/[branchId]/reviews/[reviewId]/respond:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to respond to review' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reviewId: string }> }
) {
  try {
    const { merchantId, branchId, reviewId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/respond`, {
      method: 'PUT',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/branches/[branchId]/reviews/[reviewId]/respond:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update review response' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reviewId: string }> }
) {
  try {
    const { merchantId, branchId, reviewId } = await params;
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/respond`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await handleApiResponse(response);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/branches/[branchId]/reviews/[reviewId]/respond:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete review response' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
