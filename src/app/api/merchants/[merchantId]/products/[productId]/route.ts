import { NextRequest, NextResponse } from 'next/server';
import { inventoryService } from '@/services/inventoryService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a product
const updateProductSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  price: z.number().min(0, 'Price must be a non-negative number').optional(),
  salePrice: z.number().min(0, 'Sale price must be a non-negative number').optional(),
  cost: z.number().min(0, 'Cost must be a non-negative number').optional(),
  categoryId: z.string().optional(),
  brandId: z.string().optional(),
  supplierId: z.string().optional(),
  taxRate: z.number().min(0, 'Tax rate must be a non-negative number').optional(),
  images: z.array(z.string()).optional(),
  attributes: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
  isService: z.boolean().optional(),
  inventoryTracking: z.boolean().optional(),
  stockQuantity: z.number().int().min(0, 'Stock quantity must be a non-negative integer').optional(),
  lowStockThreshold: z.number().int().min(0, 'Low stock threshold must be a non-negative integer').optional(),
});

/**
 * GET handler for fetching a specific product
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; productId: string } }
) {
  try {
    // Get the merchant ID and product ID from the URL
    const { merchantId, productId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the product
    const product = await inventoryService.getProductById(merchantId, productId);
    
    return NextResponse.json(product);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/products/[productId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a product
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; productId: string } }
) {
  try {
    // Get the merchant ID and product ID from the URL
    const { merchantId, productId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateProductSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the product
    const product = await inventoryService.updateProduct(
      merchantId,
      productId,
      validationResult.data
    );
    
    return NextResponse.json(product);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/products/[productId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a product
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; productId: string } }
) {
  try {
    // Get the merchant ID and product ID from the URL
    const { merchantId, productId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the product
    const result = await inventoryService.deleteProduct(merchantId, productId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/products/[productId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
