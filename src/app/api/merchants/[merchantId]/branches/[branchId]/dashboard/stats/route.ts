/**
 * API route for dashboard statistics
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/dashboard/stats`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    
    // Return mock data for development
    const mockData = {
      todayOrders: 45,
      todayRevenue: 1250.75,
      todayReservations: 18,
      activeStaff: 12,
      ordersGrowth: 8.5,
      revenueGrowth: 12.3,
      reservationsGrowth: -2.1
    };
    
    return NextResponse.json(mockData);
  }
}
