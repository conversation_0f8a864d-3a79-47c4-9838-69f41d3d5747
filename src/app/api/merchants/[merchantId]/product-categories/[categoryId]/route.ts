import { NextRequest, NextResponse } from 'next/server';
import { inventoryService } from '@/services/inventoryService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a product category
const updateProductCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  parentId: z.string().optional(),
  image: z.string().optional(),
  order: z.number().int().min(0, 'Order must be a non-negative integer').optional(),
});

/**
 * PUT handler for updating a product category
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; categoryId: string } }
) {
  try {
    // Get the merchant ID and category ID from the URL
    const { merchantId, categoryId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateProductCategorySchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the product category
    const category = await inventoryService.updateProductCategory(
      merchantId,
      categoryId,
      validationResult.data
    );
    
    return NextResponse.json(category);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/product-categories/[categoryId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a product category
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; categoryId: string } }
) {
  try {
    // Get the merchant ID and category ID from the URL
    const { merchantId, categoryId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the product category
    const result = await inventoryService.deleteProductCategory(merchantId, categoryId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/product-categories/[categoryId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
