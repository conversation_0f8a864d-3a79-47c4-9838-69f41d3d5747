import { NextRequest, NextResponse } from 'next/server';
import { serviceService } from '@/services/serviceService';
import { createServiceSchema, updateServiceSchema } from '@/lib/validations/serviceSchema';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }

  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);

  if (error || !user) {
    return { authenticated: false };
  }

  return { authenticated: true, user };
}

/**
 * GET handler for fetching services
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;

    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get services for the merchant
    const services = await serviceService.getServices(merchantId);

    return NextResponse.json(services);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/services:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a service
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;

    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();

    // Validate the request body
    const validationResult = createServiceSchema.safeParse({
      ...body,
      merchantId,
    });

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Create the service
    const service = await serviceService.createService(merchantId, body);

    return NextResponse.json(service, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/services:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
