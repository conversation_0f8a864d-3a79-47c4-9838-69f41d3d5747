import { NextRequest, NextResponse } from 'next/server';
import { campaignService } from '@/services';
import { supabase } from '@/lib/supabase/client';

/**
 * POST /api/merchants/[merchantId]/campaigns/[campaignId]/execute
 * Execute a campaign
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; campaignId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Execute campaign
    const execution = await campaignService.executeCampaign(
      params.merchantId,
      params.campaignId
    );

    return NextResponse.json(execution);
  } catch (error) {
    console.error('Error executing campaign:', error);
    return NextResponse.json(
      { error: 'Failed to execute campaign' },
      { status: 500 }
    );
  }
}
