import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';
import { z } from 'zod';

const cancelReservationSchema = z.object({
  reason: z.string().optional(),
});

/**
 * POST handler for cancelling a reservation
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; reservationId: string } }
) {
  try {
    // Get the merchant ID and reservation ID from the URL
    const { merchantId, reservationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = cancelReservationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { reason } = validationResult.data;
    
    // Cancel the reservation
    const reservation = await reservationService.cancelReservation(merchantId, reservationId);
    
    // If a reason was provided, update the notes
    if (reason) {
      await reservationService.updateReservation(merchantId, reservationId, {
        notes: reason
      });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Reservation cancelled successfully',
      reservation 
    });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/reservations/[reservationId]/cancel:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
