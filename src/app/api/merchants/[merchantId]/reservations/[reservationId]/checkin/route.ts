import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';
import { z } from 'zod';

const checkInSchema = z.object({
  tableId: z.string().optional(),
});

/**
 * POST handler for checking in a reservation
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; reservationId: string } }
) {
  try {
    // Get the merchant ID and reservation ID from the URL
    const { merchantId, reservationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = checkInSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { tableId } = validationResult.data;
    
    // Update the reservation status to seated and optionally update table
    const updateData: any = {
      status: 'seated'
    };
    
    if (tableId) {
      updateData.tableId = tableId;
    }
    
    const reservation = await reservationService.updateReservation(merchantId, reservationId, updateData);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Customer checked in successfully',
      reservation 
    });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/reservations/[reservationId]/checkin:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
