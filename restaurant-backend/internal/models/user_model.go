package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// User represents a staff member or admin user
type User struct {
	BaseModel
	MerchantID       uuid.UUID       `json:"merchant_id" gorm:"type:uuid;not null;index"`
	BranchID         *uuid.UUID      `json:"branch_id" gorm:"type:uuid;index"`
	Email            string          `json:"email" gorm:"type:varchar(255);uniqueIndex;not null"`
	PasswordHash     string          `json:"-" gorm:"type:varchar(255);not null"`
	FirstName        string          `json:"first_name" gorm:"type:varchar(100);not null"`
	LastName         string          `json:"last_name" gorm:"type:varchar(100);not null"`
	Phone            string          `json:"phone" gorm:"type:varchar(50)"`
	AvatarURL        string          `json:"avatar_url" gorm:"type:varchar(500)"`
	RoleID           uuid.UUID       `json:"role_id" gorm:"type:uuid;not null;index"`
	Position         string          `json:"position" gorm:"type:varchar(100)"`
	Department       string          `json:"department" gorm:"type:varchar(100)"`
	EmployeeID       string          `json:"employee_id" gorm:"type:varchar(50);uniqueIndex"`
	HireDate         time.Time       `json:"hire_date"`
	Salary           *float64        `json:"salary" gorm:"type:decimal(10,2)"`
	HourlyRate       *float64        `json:"hourly_rate" gorm:"type:decimal(8,2)"`
	Status           string          `json:"status" gorm:"type:varchar(20);default:'active'"`
	Address          Address         `json:"address" gorm:"embedded;embeddedPrefix:address_"`
	EmergencyContact ContactInfo     `json:"emergency_contact" gorm:"embedded;embeddedPrefix:emergency_"`
	Schedule         ScheduleData    `json:"schedule" gorm:"type:jsonb"`
	Performance      PerformanceData `json:"performance" gorm:"type:jsonb"`
	LastLoginAt      *time.Time      `json:"last_login_at"`

	// Relationships
	Branch *ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Role   Role        `json:"role,omitempty" gorm:"foreignKey:RoleID"`
}

// Role represents a user role with permissions
type Role struct {
	BaseModel
	MerchantID  uuid.UUID       `json:"merchant_id" gorm:"type:uuid;not null;index"`
	Name        string          `json:"name" gorm:"type:varchar(100);not null"`
	Description string          `json:"description" gorm:"type:text"`
	Permissions PermissionsData `json:"permissions" gorm:"type:jsonb"`
	IsActive    bool            `json:"is_active" gorm:"default:true"`

	// Relationships
	Users []User `json:"users,omitempty" gorm:"foreignKey:RoleID"`
}

// Permission represents a single permission
type Permission struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
	IsActive    bool   `json:"is_active"`
}

// ScheduleData represents work schedule data
type ScheduleData []WorkSchedule

// PerformanceData represents performance metrics data
type PerformanceData PerformanceMetrics

// PermissionsData represents permissions data
type PermissionsData []string

// Scan implements the sql.Scanner interface for ScheduleData
func (s *ScheduleData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, s)
}

// Value implements the driver.Valuer interface for ScheduleData
func (s ScheduleData) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface for PerformanceData
func (p *PerformanceData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, p)
}

// Value implements the driver.Valuer interface for PerformanceData
func (p PerformanceData) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan implements the sql.Scanner interface for PermissionsData
func (p *PermissionsData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, p)
}

// Value implements the driver.Valuer interface for PermissionsData
func (p PermissionsData) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// TableName specifies the table name for User
func (User) TableName() string {
	return "users"
}

// TableName specifies the table name for Role
func (Role) TableName() string {
	return "roles"
}

// BeforeCreate hook for User
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if err := u.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Generate employee ID if not provided
	if u.EmployeeID == "" {
		u.EmployeeID = generateEmployeeID()
	}

	// Set default schedule if not provided
	if len(u.Schedule) == 0 {
		u.Schedule = getDefaultSchedule()
	}

	// Initialize performance data
	if u.Performance.LastReviewDate.IsZero() {
		u.Performance = PerformanceData{
			OrdersServed:     0,
			CustomerRating:   0,
			PunctualityScore: 100,
			SalesGenerated:   0,
			LastReviewDate:   time.Now(),
		}
	}

	return nil
}

// SetPassword hashes and sets the user's password
func (u *User) SetPassword(password string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.PasswordHash = string(hashedPassword)
	return nil
}

// CheckPassword verifies the user's password
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	return u.FirstName + " " + u.LastName
}

// GetSlug returns a URL-friendly slug for the user
func (u *User) GetSlug() string {
	return slugify(u.FirstName + "-" + u.LastName)
}

// IsActive returns true if the user is active
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// HasPermission checks if the user has a specific permission
func (u *User) HasPermission(permission string) bool {
	for _, p := range u.Role.Permissions {
		if p == permission {
			return true
		}
	}
	return false
}

// HasAnyPermission checks if the user has any of the specified permissions
func (u *User) HasAnyPermission(permissions ...string) bool {
	for _, permission := range permissions {
		if u.HasPermission(permission) {
			return true
		}
	}
	return false
}

// GetWorkingDays returns the days the user is scheduled to work
func (u *User) GetWorkingDays() []int {
	var workingDays []int
	for _, schedule := range u.Schedule {
		if schedule.IsWorkingDay {
			workingDays = append(workingDays, schedule.DayOfWeek)
		}
	}
	return workingDays
}

// IsWorkingDay checks if the user is scheduled to work on a specific day
func (u *User) IsWorkingDay(dayOfWeek int) bool {
	for _, schedule := range u.Schedule {
		if schedule.DayOfWeek == dayOfWeek && schedule.IsWorkingDay {
			return true
		}
	}
	return false
}

// GetScheduleForDay returns the schedule for a specific day
func (u *User) GetScheduleForDay(dayOfWeek int) *WorkSchedule {
	for _, schedule := range u.Schedule {
		if schedule.DayOfWeek == dayOfWeek {
			return &schedule
		}
	}
	return nil
}

// HasPermission checks if the role has a specific permission
func (r *Role) HasPermission(permission string) bool {
	for _, p := range r.Permissions {
		if p == permission {
			return true
		}
	}
	return false
}

// AddPermission adds a permission to the role
func (r *Role) AddPermission(permission string) {
	if !r.HasPermission(permission) {
		r.Permissions = append(r.Permissions, permission)
	}
}

// RemovePermission removes a permission from the role
func (r *Role) RemovePermission(permission string) {
	for i, p := range r.Permissions {
		if p == permission {
			r.Permissions = append(r.Permissions[:i], r.Permissions[i+1:]...)
			break
		}
	}
}

// Helper functions

func generateEmployeeID() string {
	// Generate a simple employee ID (in production, you might want a more sophisticated approach)
	return "EMP" + uuid.New().String()[:8]
}

func getDefaultSchedule() ScheduleData {
	return ScheduleData{
		{DayOfWeek: 0, StartTime: "00:00", EndTime: "00:00", IsWorkingDay: false}, // Sunday
		{DayOfWeek: 1, StartTime: "09:00", EndTime: "17:00", IsWorkingDay: true},  // Monday
		{DayOfWeek: 2, StartTime: "09:00", EndTime: "17:00", IsWorkingDay: true},  // Tuesday
		{DayOfWeek: 3, StartTime: "09:00", EndTime: "17:00", IsWorkingDay: true},  // Wednesday
		{DayOfWeek: 4, StartTime: "09:00", EndTime: "17:00", IsWorkingDay: true},  // Thursday
		{DayOfWeek: 5, StartTime: "09:00", EndTime: "17:00", IsWorkingDay: true},  // Friday
		{DayOfWeek: 6, StartTime: "00:00", EndTime: "00:00", IsWorkingDay: false}, // Saturday
	}
}

func slugify(text string) string {
	// Simple slugify function (in production, use a proper library)
	return text // Placeholder - implement proper slugification
}

// Permission constants
const (
	PermissionViewDashboard      = "view_dashboard"
	PermissionManageOrders       = "manage_orders"
	PermissionViewOrders         = "view_orders"
	PermissionManageMenu         = "manage_menu"
	PermissionViewMenu           = "view_menu"
	PermissionManageStaff        = "manage_staff"
	PermissionViewStaff          = "view_staff"
	PermissionManageReservations = "manage_reservations"
	PermissionViewReservations   = "view_reservations"
	PermissionManageTables       = "manage_tables"
	PermissionViewTables         = "view_tables"
	PermissionManageReviews      = "manage_reviews"
	PermissionViewReviews        = "view_reviews"
	PermissionViewReports        = "view_reports"
	PermissionManageSettings     = "manage_settings"
	PermissionViewSettings       = "view_settings"
)
