import { NextRequest, NextResponse } from 'next/server';
import { paymentService } from '@/services/paymentService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for creating a payment
const createPaymentSchema = z.object({
  appointmentId: z.string().optional(),
  userId: z.string().optional(),
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().min(3, 'Currency code must be at least 3 characters'),
  status: z.enum(['pending', 'completed', 'failed', 'refunded']),
  paymentMethod: z.enum(['credit_card', 'debit_card', 'bank_transfer', 'cash', 'other']),
  paymentMethodDetails: z.record(z.any()).optional(),
  externalPaymentId: z.string().optional(),
  refundId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET handler for fetching payments
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const offset = parseInt(searchParams.get('offset') || '0', 10);
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get payments
    const result = await paymentService.getPayments(merchantId, limit, offset);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/payments:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a payment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createPaymentSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the payment
    const payment = await paymentService.createPayment({
      ...validationResult.data,
      merchantId,
    });
    
    return NextResponse.json(payment, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/payments:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
