import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';
import { getBackendAuthHeaders } from '@/lib/auth/backendAuth';

export async function GET(request: NextRequest) {
  try {
    console.log('=== AUTH TEST START ===');
    
    // Test 1: Get NextAuth session
    const session = await getServerSession(authOptions);
    console.log('NextAuth session:', session ? 'Found' : 'Not found');
    if (session) {
      console.log('Session user:', {
        id: session.user?.id,
        email: session.user?.email,
        name: session.user?.name,
        role: session.user?.role
      });
    }

    // Test 2: Get backend auth headers
    const authHeaders = await getBackendAuthHeaders(request);
    console.log('Backend auth headers:', authHeaders);

    // Test 3: Try to call backend
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8080/api/v1';
    const backendUrl = `${BACKEND_API_URL}/merchants`;
    
    console.log('Calling backend URL:', backendUrl);
    console.log('With headers:', authHeaders);

    try {
      const response = await fetch(backendUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        },
      });

      console.log('Backend response status:', response.status);
      console.log('Backend response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.log('Backend error response:', errorText);
      }

      return NextResponse.json({
        success: true,
        session: session ? {
          user: {
            id: session.user?.id,
            email: session.user?.email,
            name: session.user?.name,
            role: session.user?.role
          }
        } : null,
        authHeaders,
        backendTest: {
          url: backendUrl,
          status: response.status,
          statusText: response.statusText,
          ok: response.ok
        }
      });

    } catch (backendError) {
      console.error('Backend call failed:', backendError);
      
      return NextResponse.json({
        success: false,
        session: session ? {
          user: {
            id: session.user?.id,
            email: session.user?.email,
            name: session.user?.name,
            role: session.user?.role
          }
        } : null,
        authHeaders,
        backendError: backendError instanceof Error ? backendError.message : 'Unknown error'
      });
    }

  } catch (error) {
    console.error('Auth test error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
