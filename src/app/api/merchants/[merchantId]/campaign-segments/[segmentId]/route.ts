import { NextRequest, NextResponse } from 'next/server';
import { campaignSegmentService } from '@/services';
import { z } from 'zod';
import { supabase } from '@/lib/supabase/client';

// Schema for campaign segment filter
const campaignSegmentFilterSchema = z.object({
  field: z.string(),
  operator: z.enum([
    'equals',
    'not_equals',
    'contains',
    'not_contains',
    'greater_than',
    'less_than',
    'in',
    'not_in',
  ]),
  value: z.any(),
});

// Schema for updating a campaign segment
const updateCampaignSegmentSchema = z.object({
  name: z.string().min(1, 'Segment name is required').optional(),
  description: z.string().optional(),
  filters: z.array(campaignSegmentFilterSchema).min(1, 'At least one filter is required').optional(),
});

/**
 * GET /api/merchants/[merchantId]/campaign-segments/[segmentId]
 * Get a specific campaign segment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; segmentId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get segment
    const segment = await campaignSegmentService.getSegmentById(
      params.merchantId,
      params.segmentId
    );

    if (!segment) {
      return NextResponse.json({ error: 'Segment not found' }, { status: 404 });
    }

    return NextResponse.json(segment);
  } catch (error) {
    console.error('Error fetching campaign segment:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaign segment' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/merchants/[merchantId]/campaign-segments/[segmentId]
 * Update a campaign segment
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; segmentId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateCampaignSegmentSchema.parse(body);

    // Update segment
    const segment = await campaignSegmentService.updateSegment(
      params.merchantId,
      params.segmentId,
      validatedData
    );

    return NextResponse.json(segment);
  } catch (error) {
    console.error('Error updating campaign segment:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to update campaign segment' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/merchants/[merchantId]/campaign-segments/[segmentId]
 * Delete a campaign segment
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; segmentId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Delete segment
    await campaignSegmentService.deleteSegment(
      params.merchantId,
      params.segmentId
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting campaign segment:', error);
    return NextResponse.json(
      { error: 'Failed to delete campaign segment' },
      { status: 500 }
    );
  }
}
