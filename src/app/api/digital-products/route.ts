import { NextRequest, NextResponse } from 'next/server';
import { server<PERSON>etchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const merchantId = searchParams.get('merchantId');
    
    if (!merchantId) {
      return NextResponse.json(
        { error: 'merchantId is required' },
        { status: 400 }
      );
    }

    // Remove merchantId from search params for backend call
    const backendParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      if (key !== 'merchantId') {
        backendParams.append(key, value);
      }
    });

    const queryString = backendParams.toString();
    const url = `/merchants/${merchantId}/digital-products${queryString ? `?${queryString}` : ''}`;

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/digital-products:', error);
    
    // Return empty array as fallback for development
    return NextResponse.json([]);
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { merchantId, ...productData } = body;
    
    if (!merchantId) {
      return NextResponse.json(
        { error: 'merchantId is required' },
        { status: 400 }
      );
    }

    const response = await serverFetchClient(`/merchants/${merchantId}/digital-products`, request, {
      method: 'POST',
      body: JSON.stringify(productData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/digital-products:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create digital product' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
