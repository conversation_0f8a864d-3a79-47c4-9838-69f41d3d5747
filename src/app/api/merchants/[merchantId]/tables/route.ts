import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { createTableSchema } from '@/lib/validations/reservationSchema';
import { checkAuth } from '@/lib/auth';

/**
 * GET handler for fetching tables
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get tables for the merchant
    const tables = await reservationService.getTables(merchantId);
    
    return NextResponse.json(tables);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/tables:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a table
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createTableSchema.safeParse({
      ...body,
      merchantId,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the table
    const table = await reservationService.createTable(merchantId, body);
    
    return NextResponse.json(table, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/tables:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
