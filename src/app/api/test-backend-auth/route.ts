import { NextRequest, NextResponse } from 'next/server'
import { getBackendAuthHeaders } from '@/lib/auth/backendAuth'

export async function GET(request: NextRequest) {
  try {
    // Get the authentication headers that would be sent to backend
    const authHeaders = await getBackendAuthHeaders(request)
    
    return NextResponse.json({
      message: 'Backend authentication test',
      headers: authHeaders,
      hasAuth: Object.keys(authHeaders).length > 0,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Backend auth test error:', error)
    return NextResponse.json(
      { error: 'Failed to test backend auth' },
      { status: 500 }
    )
  }
}
