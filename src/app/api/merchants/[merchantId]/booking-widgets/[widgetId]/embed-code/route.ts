import { NextRequest, NextResponse } from 'next/server';
import { bookingWidgetService } from '@/services/bookingWidgetService';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * GET handler for fetching embed code
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; widgetId: string } }
) {
  try {
    // Get the merchant ID and widget ID from the URL
    const { merchantId, widgetId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the embed code
    const result = await bookingWidgetService.getEmbedCode(merchantId, widgetId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/booking-widgets/[widgetId]/embed-code:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
