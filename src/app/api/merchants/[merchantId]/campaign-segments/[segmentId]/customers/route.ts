import { NextRequest, NextResponse } from 'next/server';
import { campaignSegmentService } from '@/services';
import { supabase } from '@/lib/supabase/client';

/**
 * GET /api/merchants/[merchantId]/campaign-segments/[segmentId]/customers
 * Get customers in a campaign segment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; segmentId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '100', 10);
    const offset = parseInt(searchParams.get('offset') || '0', 10);

    // Get customers in segment
    const result = await campaignSegmentService.getSegmentCustomers(
      params.merchantId,
      params.segmentId,
      limit,
      offset
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching segment customers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch segment customers' },
      { status: 500 }
    );
  }
}
