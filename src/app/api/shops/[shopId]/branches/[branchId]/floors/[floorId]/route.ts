/**
 * API route for individual floor management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string; floorId: string }> }
) {
  try {
    const { shopId, branchId, floorId } = await params;
    const url = `/shops/${shopId}/branches/${branchId}/floors/${floorId}`;

    const response = await serverFetchClient(url, request, {
      method: 'GET',
    });

    return handleApiResponse(response);
  } catch (error) {
    console.error('Error fetching floor:', error);
    return NextResponse.json(
      { error: 'Failed to fetch floor' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string; floorId: string }> }
) {
  try {
    const { shopId, branchId, floorId } = await params;
    const body = await request.json();
    const url = `/shops/${shopId}/branches/${branchId}/floors/${floorId}`;

    const response = await serverFetchClient(url, request, {
      method: 'PUT',
      body: JSON.stringify(body),
    });

    return handleApiResponse(response);
  } catch (error) {
    console.error('Error updating floor:', error);
    return NextResponse.json(
      { error: 'Failed to update floor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string; floorId: string }> }
) {
  try {
    const { shopId, branchId, floorId } = await params;
    const url = `/shops/${shopId}/branches/${branchId}/floors/${floorId}`;

    const response = await serverFetchClient(url, request, {
      method: 'DELETE',
    });

    return handleApiResponse(response);
  } catch (error) {
    console.error('Error deleting floor:', error);
    return NextResponse.json(
      { error: 'Failed to delete floor' },
      { status: 500 }
    );
  }
}
