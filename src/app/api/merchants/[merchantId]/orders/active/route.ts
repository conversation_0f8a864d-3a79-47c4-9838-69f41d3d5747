import { NextRequest, NextResponse } from 'next/server';
import { checkAuth } from '@/lib/auth';

// Mock orders data (imported from the main orders route)
import { orders } from '../route';

/**
 * GET handler for fetching active orders
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Use the merchant ID directly from params
    const merchantId = params.merchantId;

    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get orders for the merchant
    const merchantOrders = orders[merchantId as keyof typeof orders] || [];

    // Filter active orders (pending, preparing, ready)
    const activeOrders = merchantOrders.filter((order: any) =>
      ['pending', 'preparing', 'ready'].includes(order.status)
    );

    return NextResponse.json(activeOrders);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/orders/active:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
