import { NextRequest, NextResponse } from 'next/server';
import { paymentService } from '@/services/paymentService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a payment
const updatePaymentSchema = z.object({
  status: z.enum(['pending', 'completed', 'failed', 'refunded']).optional(),
  paymentMethod: z.enum(['credit_card', 'debit_card', 'bank_transfer', 'cash', 'other']).optional(),
  paymentMethodDetails: z.record(z.any()).optional(),
  externalPaymentId: z.string().optional(),
  refundId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET handler for fetching a specific payment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; paymentId: string } }
) {
  try {
    // Get the merchant ID and payment ID from the URL
    const { merchantId, paymentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the payment
    const payment = await paymentService.getPaymentById(merchantId, paymentId);
    
    return NextResponse.json(payment);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/payments/[paymentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a payment
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; paymentId: string } }
) {
  try {
    // Get the merchant ID and payment ID from the URL
    const { merchantId, paymentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updatePaymentSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the payment
    const payment = await paymentService.updatePayment(
      merchantId,
      paymentId,
      validationResult.data
    );
    
    return NextResponse.json(payment);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/payments/[paymentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
