package types

import (
	"time"

	"github.com/google/uuid"
)

// User request/response types
type CreateUserRequest struct {
	Email            string             `json:"email" binding:"required,email"`
	Password         string             `json:"password" binding:"required,min=6"`
	FirstName        string             `json:"first_name" binding:"required"`
	LastName         string             `json:"last_name" binding:"required"`
	Phone            string             `json:"phone"`
	RoleID           uuid.UUID          `json:"role_id" binding:"required"`
	Position         string             `json:"position"`
	Department       string             `json:"department"`
	EmployeeID       string             `json:"employee_id"`
	HireDate         *time.Time         `json:"hire_date"`
	Salary           *float64           `json:"salary" binding:"omitempty,min=0"`
	HourlyRate       *float64           `json:"hourly_rate" binding:"omitempty,min=0"`
	BranchID         *uuid.UUID         `json:"branch_id"`
	AvatarURL        string             `json:"avatar_url"`
	Address          AddressRequest     `json:"address"`
	EmergencyContact ContactInfoRequest `json:"emergency_contact"`
}

type UpdateUserRequest struct {
	FirstName  *string    `json:"first_name"`
	LastName   *string    `json:"last_name"`
	Phone      *string    `json:"phone"`
	RoleID     *uuid.UUID `json:"role_id"`
	Position   *string    `json:"position"`
	Department *string    `json:"department"`
	EmployeeID *string    `json:"employee_id"`
	HireDate   *time.Time `json:"hire_date"`
	Salary     *float64   `json:"salary" binding:"omitempty,min=0"`
	HourlyRate *float64   `json:"hourly_rate" binding:"omitempty,min=0"`
	BranchID   *uuid.UUID `json:"branch_id"`
	AvatarURL  *string    `json:"avatar_url"`
	Status     *string    `json:"status"`
}

type UpdateUserStatusRequest struct {
	Status string `json:"status" binding:"required,oneof=active inactive suspended"`
}

type UserFilters struct {
	// Filtering
	RoleID     *uuid.UUID `form:"role_id"`
	BranchID   *uuid.UUID `form:"branch_id"`
	Position   string     `form:"position"`
	Department string     `form:"department"`
	Status     string     `form:"status"`
	Search     string     `form:"search"`

	// Sorting
	SortBy    string `form:"sort_by"`    // first_name, last_name, position, department, status, hire_date, created_at
	SortOrder string `form:"sort_order"` // asc, desc

	// Pagination
	Page  int `form:"page" binding:"min=1"`
	Limit int `form:"limit" binding:"min=1,max=100"`
}

// Role request/response types
type CreateRoleRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description"`
	Permissions []string `json:"permissions"`
}

type UpdateRoleRequest struct {
	Name        *string  `json:"name"`
	Description *string  `json:"description"`
	Permissions []string `json:"permissions"`
	IsActive    *bool    `json:"is_active"`
}

// Supporting types
type ContactInfoRequest struct {
	Name         string `json:"name"`
	Relationship string `json:"relationship"`
	Phone        string `json:"phone"`
	Email        string `json:"email"`
}

// Response types
type UserResponse struct {
	ID               uuid.UUID           `json:"id"`
	MerchantID       uuid.UUID           `json:"merchant_id"`
	BranchID         *uuid.UUID          `json:"branch_id"`
	Email            string              `json:"email"`
	FirstName        string              `json:"first_name"`
	LastName         string              `json:"last_name"`
	Phone            string              `json:"phone"`
	AvatarURL        string              `json:"avatar_url"`
	RoleID           uuid.UUID           `json:"role_id"`
	Role             *RoleResponse       `json:"role,omitempty"`
	Position         string              `json:"position"`
	Department       string              `json:"department"`
	EmployeeID       string              `json:"employee_id"`
	HireDate         time.Time           `json:"hire_date"`
	Salary           *float64            `json:"salary"`
	HourlyRate       *float64            `json:"hourly_rate"`
	Status           string              `json:"status"`
	Address          AddressResponse     `json:"address"`
	EmergencyContact ContactInfoResponse `json:"emergency_contact"`
	LastLoginAt      *time.Time          `json:"last_login_at"`
	CreatedAt        time.Time           `json:"created_at"`
	UpdatedAt        time.Time           `json:"updated_at"`
}

type UsersResponse struct {
	Data       []UserResponse `json:"data"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	Limit      int            `json:"limit"`
	TotalPages int            `json:"total_pages"`
}

type RoleResponse struct {
	ID          uuid.UUID `json:"id"`
	MerchantID  uuid.UUID `json:"merchant_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Permissions []string  `json:"permissions"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type RolesResponse struct {
	Data       []RoleResponse `json:"data"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	Limit      int            `json:"limit"`
	TotalPages int            `json:"total_pages"`
}

type ContactInfoResponse struct {
	Name         string `json:"name"`
	Relationship string `json:"relationship"`
	Phone        string `json:"phone"`
	Email        string `json:"email"`
}

// Permission constants
const (
	UserStatusActive    = "active"
	UserStatusInactive  = "inactive"
	UserStatusSuspended = "suspended"
)

// Default roles
const (
	RoleAdmin   = "admin"
	RoleManager = "manager"
	RoleStaff   = "staff"
	RoleWaiter  = "waiter"
	RoleChef    = "chef"
	RoleCashier = "cashier"
	RoleHost    = "host"
)
