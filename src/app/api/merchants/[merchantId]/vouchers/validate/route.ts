import { NextRequest, NextResponse } from 'next/server';
import { voucherService } from '@/services/voucherService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for validating a voucher
const validateVoucherSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  amount: z.number().positive('Amount must be positive'),
  serviceId: z.string().optional(),
});

/**
 * POST handler for validating a voucher
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = validateVoucherSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Validate the voucher
    const { code, amount, serviceId } = validationResult.data;
    const result = await voucherService.validateVoucher(merchantId, code, amount, serviceId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/vouchers/validate:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
