import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Mock financial data generator
const generateMockFinancialData = (dateRange: string) => {
  const now = new Date();
  const months = dateRange === '7d' ? 1 : dateRange === '30d' ? 1 : dateRange === '90d' ? 3 : 12;
  
  // Generate monthly financial data
  const financialData = [];
  
  for (let i = months - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setMonth(date.getMonth() - i);
    
    const revenue = 75000 + Math.random() * 15000;
    const expenses = revenue * (0.6 + Math.random() * 0.1); // 60-70% of revenue
    const netProfit = revenue - expenses;
    
    financialData.push({
      month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      revenue: Math.round(revenue),
      expenses: Math.round(expenses),
      netProfit: Math.round(netProfit),
      profitMargin: Math.round((netProfit / revenue) * 100 * 10) / 10,
    });
  }
  
  return financialData;
};

export async function GET(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '30d';
    const branchId = searchParams.get('branchId');

    // Generate mock data
    const financialData = generateMockFinancialData(dateRange);
    
    // Calculate current period totals
    const currentPeriod = financialData[financialData.length - 1] || {
      revenue: 84250,
      expenses: 52180,
      netProfit: 32070,
      profitMargin: 38.1,
    };
    
    const metrics = {
      totalRevenue: {
        value: `$${currentPeriod.revenue.toLocaleString()}`,
        change: '+12.5%',
        trend: 'up',
      },
      totalExpenses: {
        value: `$${currentPeriod.expenses.toLocaleString()}`,
        change: '+8.3%',
        trend: 'up',
      },
      netProfit: {
        value: `$${currentPeriod.netProfit.toLocaleString()}`,
        change: '+18.7%',
        trend: 'up',
      },
      profitMargin: {
        value: `${currentPeriod.profitMargin}%`,
        change: '+2.4%',
        trend: 'up',
      },
    };

    // Expense breakdown
    const expenseCategories = [
      {
        name: 'Food & Beverages',
        amount: Math.round(currentPeriod.expenses * 0.545),
        percentage: 54.5,
        change: '+5.2%',
        trend: 'up',
        color: 'blue',
      },
      {
        name: 'Staff Salaries',
        amount: Math.round(currentPeriod.expenses * 0.291),
        percentage: 29.1,
        change: '+3.1%',
        trend: 'up',
        color: 'green',
      },
      {
        name: 'Rent & Utilities',
        amount: Math.round(currentPeriod.expenses * 0.092),
        percentage: 9.2,
        change: '+0.5%',
        trend: 'up',
        color: 'yellow',
      },
      {
        name: 'Marketing',
        amount: Math.round(currentPeriod.expenses * 0.040),
        percentage: 4.0,
        change: '-12.3%',
        trend: 'down',
        color: 'purple',
      },
      {
        name: 'Equipment',
        amount: Math.round(currentPeriod.expenses * 0.019),
        percentage: 1.9,
        change: '+45.2%',
        trend: 'up',
        color: 'red',
      },
      {
        name: 'Other',
        amount: Math.round(currentPeriod.expenses * 0.013),
        percentage: 1.3,
        change: '-8.7%',
        trend: 'down',
        color: 'gray',
      },
    ];

    // Profit & Loss statement
    const profitLossData = [
      // Revenue items
      {
        category: 'Food Sales',
        amount: Math.round(currentPeriod.revenue * 0.813),
        percentage: 81.3,
        type: 'revenue',
      },
      {
        category: 'Beverage Sales',
        amount: Math.round(currentPeriod.revenue * 0.152),
        percentage: 15.2,
        type: 'revenue',
      },
      {
        category: 'Other Revenue',
        amount: Math.round(currentPeriod.revenue * 0.035),
        percentage: 3.5,
        type: 'revenue',
      },
      // Expense items
      {
        category: 'Cost of Goods Sold',
        amount: -Math.round(currentPeriod.expenses * 0.545),
        percentage: 33.8,
        type: 'expense',
      },
      {
        category: 'Labor Costs',
        amount: -Math.round(currentPeriod.expenses * 0.291),
        percentage: 18.0,
        type: 'expense',
      },
      {
        category: 'Operating Expenses',
        amount: -Math.round(currentPeriod.expenses * 0.164),
        percentage: 10.1,
        type: 'expense',
      },
    ];

    // Cash flow data
    const cashFlowData = financialData.map(period => ({
      month: period.month,
      inflow: period.revenue,
      outflow: -period.expenses,
      net: period.netProfit,
    }));

    // Key financial ratios
    const keyRatios = {
      grossProfitMargin: 66.2,
      operatingMargin: currentPeriod.profitMargin,
      foodCostPercentage: 33.8,
      laborCostPercentage: 18.0,
      currentRatio: 2.1,
      quickRatio: 1.8,
    };

    // Budget vs actual
    const budgetComparison = {
      revenue: {
        actual: currentPeriod.revenue,
        budget: 80000,
        variance: ((currentPeriod.revenue - 80000) / 80000 * 100).toFixed(1),
      },
      expenses: {
        actual: currentPeriod.expenses,
        budget: 48000,
        variance: ((currentPeriod.expenses - 48000) / 48000 * 100).toFixed(1),
      },
      netProfit: {
        actual: currentPeriod.netProfit,
        budget: 32000,
        variance: ((currentPeriod.netProfit - 32000) / 32000 * 100).toFixed(1),
      },
    };

    // Financial alerts
    const alerts = [
      {
        type: 'warning',
        title: 'Food costs increasing',
        message: 'Up 5.2% from last month',
        severity: 'medium',
      },
      {
        type: 'success',
        title: 'Revenue target exceeded',
        message: '5.2% above monthly goal',
        severity: 'low',
      },
      {
        type: 'info',
        title: 'Equipment expenses spike',
        message: 'New kitchen equipment purchase',
        severity: 'low',
      },
    ];

    return NextResponse.json({
      metrics,
      financialData,
      expenseCategories,
      profitLossData,
      cashFlowData,
      keyRatios,
      budgetComparison,
      alerts,
      dateRange,
      generatedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Financial data API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
