import { NextRequest, NextResponse } from 'next/server';
import { campaignService } from '@/services';
import { z } from 'zod';
import { supabase } from '@/lib/supabase/client';

// Schema for creating a campaign
const createCampaignSchema = z.object({
  name: z.string().min(1, 'Campaign name is required'),
  type: z.enum(['email', 'sms'], {
    errorMap: () => ({ message: 'Type must be either email or sms' }),
  }),
  templateId: z.string().min(1, 'Template ID is required'),
  recipients: z.array(z.string()).min(1, 'At least one recipient is required'),
  scheduledFor: z.string().optional().transform(val => val ? new Date(val) : undefined),
  status: z.enum(['draft', 'scheduled', 'in_progress', 'completed', 'cancelled']).default('draft'),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET /api/merchants/[merchantId]/campaigns
 * Get all campaigns for a merchant
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type') as 'email' | 'sms' | undefined;

    // Get campaigns
    const campaigns = await campaignService.getCampaigns(
      params.merchantId,
      type
    );

    return NextResponse.json(campaigns);
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaigns' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/merchants/[merchantId]/campaigns
 * Create a new campaign
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createCampaignSchema.parse({
      ...body,
    });

    // Create campaign
    const campaign = await campaignService.createCampaign({
      ...validatedData,
      merchantId: params.merchantId,
    });

    return NextResponse.json(campaign, { status: 201 });
  } catch (error) {
    console.error('Error creating campaign:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to create campaign' },
      { status: 500 }
    );
  }
}
