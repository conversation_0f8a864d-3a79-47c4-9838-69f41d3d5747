/**
 * API route for menu items
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/menu/items${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching menu items:', error);
    
    // Return mock data for development
    const mockData = {
      data: [
        {
          id: '1',
          name: 'Spicy Chicken Sandwich',
          slug: 'spicy-chicken-sandwich',
          description: 'Crispy chicken breast with spicy mayo, lettuce, and tomato on a brioche bun',
          price: 12.99,
          categoryId: 'cat-1',
          categoryName: 'Main Course',
          image: '/images/menu/chicken-sandwich.jpg',
          ingredients: ['Chicken breast', 'Brioche bun', 'Lettuce', 'Tomato', 'Spicy mayo'],
          allergens: ['Gluten', 'Eggs'],
          nutritionalInfo: {
            calories: 520,
            protein: 35,
            carbs: 45,
            fat: 22
          },
          preparationTime: 15,
          isAvailable: true,
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          isSpicy: true,
          spiceLevel: 3,
          tags: ['Popular', 'Spicy'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Avocado Toast',
          slug: 'avocado-toast',
          description: 'Fresh avocado on sourdough bread with cherry tomatoes and feta cheese',
          price: 8.99,
          categoryId: 'cat-2',
          categoryName: 'Appetizer',
          image: '/images/menu/avocado-toast.jpg',
          ingredients: ['Sourdough bread', 'Avocado', 'Cherry tomatoes', 'Feta cheese'],
          allergens: ['Gluten', 'Dairy'],
          nutritionalInfo: {
            calories: 320,
            protein: 12,
            carbs: 28,
            fat: 18
          },
          preparationTime: 8,
          isAvailable: true,
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          isSpicy: false,
          spiceLevel: 0,
          tags: ['Healthy', 'Vegetarian'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Classic Burger',
          slug: 'classic-burger',
          description: 'Beef patty with lettuce, tomato, onion, and special sauce',
          price: 10.99,
          categoryId: 'cat-1',
          categoryName: 'Main Course',
          image: '/images/menu/classic-burger.jpg',
          ingredients: ['Beef patty', 'Burger bun', 'Lettuce', 'Tomato', 'Onion', 'Special sauce'],
          allergens: ['Gluten', 'Eggs'],
          nutritionalInfo: {
            calories: 480,
            protein: 28,
            carbs: 35,
            fat: 25
          },
          preparationTime: 12,
          isAvailable: true,
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          isSpicy: false,
          spiceLevel: 0,
          tags: ['Classic'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 3,
        itemsPerPage: 20,
        hasNextPage: false,
        hasPreviousPage: false
      }
    };
    
    return NextResponse.json(mockData);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/menu/items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating menu item:', error);
    
    // Return mock created item for development
    const mockItem = {
      id: `item-${Date.now()}`,
      name: 'New Menu Item',
      slug: 'new-menu-item',
      description: 'A delicious new item',
      price: 9.99,
      categoryId: 'cat-1',
      categoryName: 'Main Course',
      isAvailable: true,
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
      isSpicy: false,
      spiceLevel: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return NextResponse.json(mockItem);
  }
}
