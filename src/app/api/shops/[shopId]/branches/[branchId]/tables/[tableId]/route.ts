/**
 * API route for individual table management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string; tableId: string }> }
) {
  try {
    const { shopId, branchId, tableId } = await params;
    const url = `/shops/${shopId}/branches/${branchId}/tables/${tableId}`;

    console.log('Fetching table from backend:', url);

    const response = await serverFetchClient(url, request, {
      method: 'GET',
    });

    console.log('Backend response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error response:', errorText);
      throw new Error(`Backend responded with status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Successfully parsed table data:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching table:', error);
    return NextResponse.json(
      { error: 'Failed to fetch table', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string; tableId: string }> }
) {
  try {
    const { shopId, branchId, tableId } = await params;
    const body = await request.json();
    const url = `/shops/${shopId}/branches/${branchId}/tables/${tableId}`;

    console.log('Updating table in backend:', url, 'with data:', body);
    console.log('Table ID being updated:', tableId);

    const response = await serverFetchClient(url, request, {
      method: 'PUT',
      body: JSON.stringify(body),
    });

    console.log('Backend response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error response:', errorText);
      throw new Error(`Backend responded with status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Successfully updated table:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error updating table:', error);
    return NextResponse.json(
      { error: 'Failed to update table', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string; tableId: string }> }
) {
  try {
    const { shopId, branchId, tableId } = await params;
    const url = `/shops/${shopId}/branches/${branchId}/tables/${tableId}`;

    console.log('Deleting table from backend:', url);

    const response = await serverFetchClient(url, request, {
      method: 'DELETE',
    });

    console.log('Backend response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error response:', errorText);
      throw new Error(`Backend responded with status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Successfully deleted table:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error deleting table:', error);
    return NextResponse.json(
      { error: 'Failed to delete table', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
