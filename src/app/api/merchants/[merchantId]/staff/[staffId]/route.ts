import { NextRequest, NextResponse } from 'next/server';
import { staffService } from '@/services/staffService';
import { staffSchema } from '@/lib/validations/serviceSchema';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * GET handler for fetching a specific staff member
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; staffId: string } }
) {
  try {
    // Get the merchant ID and staff ID from the URL
    const { merchantId, staffId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the staff member
    const staff = await staffService.getStaffById(merchantId, staffId);
    
    return NextResponse.json(staff);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/staff/[staffId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a specific staff member
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; staffId: string } }
) {
  try {
    // Get the merchant ID and staff ID from the URL
    const { merchantId, staffId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = staffSchema.partial().safeParse({
      ...body,
      merchantId,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the staff member
    const staff = await staffService.updateStaff(merchantId, staffId, body);
    
    return NextResponse.json(staff);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/staff/[staffId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a specific staff member
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; staffId: string } }
) {
  try {
    // Get the merchant ID and staff ID from the URL
    const { merchantId, staffId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the staff member
    await staffService.deleteStaff(merchantId, staffId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/staff/[staffId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
