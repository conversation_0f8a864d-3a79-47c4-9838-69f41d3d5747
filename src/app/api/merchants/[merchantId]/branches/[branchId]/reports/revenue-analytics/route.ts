/**
 * API route for revenue analytics
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/reports/revenue-analytics${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching revenue analytics:', error);
    
    // Return mock data for development
    const mockData = {
      totalRevenue: 72050,
      revenueGrowth: 12.5,
      averageOrderValue: 21.21,
      totalOrders: 472,
      orderGrowth: 8.3
    };
    
    return NextResponse.json(mockData);
  }
}
