import { NextRequest, NextResponse } from 'next/server';
import { loyaltyService } from '@/services/loyaltyService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a loyalty program
const updateLoyaltyProgramSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  pointsPerPurchase: z.number().int().min(0, 'Points per purchase must be a non-negative integer').optional(),
  pointsPerAmount: z.number().int().min(0, 'Points per amount must be a non-negative integer').optional(),
  amountPerPoint: z.number().positive('Amount per point must be positive').optional(),
  minimumPointsRedemption: z.number().int().min(0, 'Minimum points redemption must be a non-negative integer').optional(),
  pointsValueInCurrency: z.number().positive('Points value in currency must be positive').optional(),
  expirationMonths: z.number().int().min(0, 'Expiration months must be a non-negative integer').optional(),
  isActive: z.boolean().optional(),
});

/**
 * GET handler for fetching a specific loyalty program
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; programId: string } }
) {
  try {
    // Get the merchant ID and program ID from the URL
    const { merchantId, programId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the loyalty program
    const program = await loyaltyService.getLoyaltyProgramById(merchantId, programId);
    
    return NextResponse.json(program);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/loyalty-programs/[programId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a loyalty program
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; programId: string } }
) {
  try {
    // Get the merchant ID and program ID from the URL
    const { merchantId, programId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateLoyaltyProgramSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the loyalty program
    const program = await loyaltyService.updateLoyaltyProgram(
      merchantId,
      programId,
      validationResult.data
    );
    
    return NextResponse.json(program);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/loyalty-programs/[programId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a loyalty program
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; programId: string } }
) {
  try {
    // Get the merchant ID and program ID from the URL
    const { merchantId, programId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the loyalty program
    const result = await loyaltyService.deleteLoyaltyProgram(merchantId, programId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/loyalty-programs/[programId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
