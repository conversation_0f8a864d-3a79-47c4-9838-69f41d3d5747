import { NextRequest, NextResponse } from 'next/server';
import { campaignService } from '@/services';
import { z } from 'zod';
import { supabase } from '@/lib/supabase/client';

// Schema for updating a campaign
const updateCampaignSchema = z.object({
  name: z.string().min(1, 'Campaign name is required').optional(),
  templateId: z.string().min(1, 'Template ID is required').optional(),
  recipients: z.array(z.string()).min(1, 'At least one recipient is required').optional(),
  scheduledFor: z.string().optional().transform(val => val ? new Date(val) : undefined),
  status: z.enum(['draft', 'scheduled', 'in_progress', 'completed', 'cancelled']).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET /api/merchants/[merchantId]/campaigns/[campaignId]
 * Get a specific campaign
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; campaignId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get campaign
    const campaign = await campaignService.getCampaignById(
      params.merchantId,
      params.campaignId
    );

    if (!campaign) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    return NextResponse.json(campaign);
  } catch (error) {
    console.error('Error fetching campaign:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaign' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/merchants/[merchantId]/campaigns/[campaignId]
 * Update a campaign
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; campaignId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateCampaignSchema.parse(body);

    // Update campaign
    const campaign = await campaignService.updateCampaign(
      params.merchantId,
      params.campaignId,
      validatedData
    );

    return NextResponse.json(campaign);
  } catch (error) {
    console.error('Error updating campaign:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to update campaign' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/merchants/[merchantId]/campaigns/[campaignId]
 * Delete a campaign
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; campaignId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Delete campaign
    await campaignService.deleteCampaign(
      params.merchantId,
      params.campaignId
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting campaign:', error);
    return NextResponse.json(
      { error: 'Failed to delete campaign' },
      { status: 500 }
    );
  }
}
