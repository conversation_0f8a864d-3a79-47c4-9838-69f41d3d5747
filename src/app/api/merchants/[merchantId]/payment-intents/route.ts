import { NextRequest, NextResponse } from 'next/server';
import { paymentService } from '@/services/paymentService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for creating a payment intent
const createPaymentIntentSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().min(3, 'Currency code must be at least 3 characters'),
  appointmentId: z.string().optional(),
  userId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * POST handler for creating a payment intent
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated, user } = await checkAuth(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createPaymentIntentSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the payment intent
    const { amount, currency, appointmentId, userId, metadata } = validationResult.data;
    
    const paymentIntent = await paymentService.createPaymentIntent(
      merchantId,
      amount,
      currency,
      appointmentId,
      userId || user.id,
      metadata
    );
    
    return NextResponse.json(paymentIntent, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/payment-intents:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
