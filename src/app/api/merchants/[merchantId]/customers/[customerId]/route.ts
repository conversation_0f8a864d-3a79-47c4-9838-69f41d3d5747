import { NextRequest, NextResponse } from 'next/server';
import { customerService } from '@/services/customerService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a customer
const updateCustomerSchema = z.object({
  userId: z.string().optional(),
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  source: z.string().optional(),
  status: z.enum(['active', 'inactive', 'blocked']).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET handler for fetching a specific customer
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; customerId: string } }
) {
  try {
    // Get the merchant ID and customer ID from the URL
    const { merchantId, customerId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the customer
    const customer = await customerService.getCustomerById(merchantId, customerId);
    
    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/customers/[customerId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a customer
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; customerId: string } }
) {
  try {
    // Get the merchant ID and customer ID from the URL
    const { merchantId, customerId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateCustomerSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the customer
    const customer = await customerService.updateCustomer(
      merchantId,
      customerId,
      validationResult.data
    );
    
    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/customers/[customerId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a customer
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; customerId: string } }
) {
  try {
    // Get the merchant ID and customer ID from the URL
    const { merchantId, customerId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the customer
    const result = await customerService.deleteCustomer(merchantId, customerId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/customers/[customerId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
