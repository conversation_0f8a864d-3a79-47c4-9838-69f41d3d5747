/**
 * API route for shop branches management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string }> }
) {
  try {
    const { shopId } = await params;
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/shops/${shopId}/branches${queryString ? `?${queryString}` : ''}`;
    
    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/shops/[shopId]/branches:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch shop branches' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string }> }
) {
  try {
    const { shopId } = await params;
    const body = await request.json();
    
    const response = await serverFetchClient(`/shops/${shopId}/branches`, request, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/shops/[shopId]/branches:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create shop branch' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
