/**
 * API route for table areas management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/shops/${merchantId}/branches/${branchId}/tables/areas${queryString ? `?${queryString}` : ''}`;

    const response = await serverFetchClient(url, {
      method: 'GET',
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/branches/[branchId]/tables/areas:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch table areas' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const body = await request.json();

    const response = await serverFetchClient(`/shops/${merchantId}/branches/${branchId}/tables/areas`, {
      method: 'POST',
      body: JSON.stringify(body),
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/branches/[branchId]/tables/areas:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create table area' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
