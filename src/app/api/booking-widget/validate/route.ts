import { NextRequest, NextResponse } from 'next/server';
import { bookingWidgetService } from '@/services/bookingWidgetService';

/**
 * POST handler for validating a booking widget API key
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { apiKey } = body;
    
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key is required' },
        { status: 400 }
      );
    }
    
    // Get the origin of the request
    const origin = request.headers.get('origin') || '';
    
    // Validate the API key
    const widget = await bookingWidgetService.validateApiKey(apiKey, origin);
    
    // Return the widget without the API key
    const { apiKey: _, ...widgetWithoutApiKey } = widget;
    
    return NextResponse.json(widgetWithoutApiKey);
  } catch (error) {
    console.error('Error in POST /api/booking-widget/validate:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
