'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import {
  useGetLicensesQuery,
  useGetDigitalProductsQuery,
  useRevokeLicenseMutation
} from '@/lib/redux/api/endpoints/digitalApi';

interface LicensesPageProps {
  params: {
    id: string;
  };
}

export default function LicensesPage({ params }: LicensesPageProps) {
  const { id } = params;
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const { data: licenses, isLoading: isLoadingLicenses } = useGetLicensesQuery({ merchantId: id });
  const { data: products, isLoading: isLoadingProducts } = useGetDigitalProductsQuery(id);
  const [revokeLicense, { isLoading: isRevoking }] = useRevokeLicenseMutation();

  const isLoading = isLoadingMerchants || isLoadingLicenses || isLoadingProducts;

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'digital');

  // Filter licenses based on search term and status
  const filteredLicenses = licenses?.filter(license => {
    const matchesSearch = searchTerm === '' ||
      license.licenseKey.toLowerCase().includes(searchTerm.toLowerCase()) ||
      license.userId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      license.orderId.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === null || license.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Get product name by ID
  const getProductName = (productId: string) => {
    const product = products?.find(p => p.id === productId);
    return product ? product.name : 'Unknown Product';
  };

  // Handle license revocation
  const handleRevokeLicense = async (licenseId: string) => {
    if (window.confirm('Are you sure you want to revoke this license? This action cannot be undone.')) {
      try {
        await revokeLicense({
          merchantId: id,
          licenseId,
        }).unwrap();
      } catch (error) {
        console.error('Failed to revoke license:', error);
      }
    }
  };

  if (isLoading) {
    return <div className="p-6">Loading data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Digital store not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/digital/${id}`} className="text-blue-600 hover:underline">
          ← Back to digital store
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Licenses - {merchant.name}</h1>
        <Link
          href={`/admin/merchants/digital/${id}/licenses/create`}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Create License
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
        <div className="p-4 border-b">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Search licenses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="absolute left-3 top-2.5">🔍</span>
            </div>

            <div>
              <select
                value={statusFilter || ''}
                onChange={(e) => setStatusFilter(e.target.value === '' ? null : e.target.value)}
                className="px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="expired">Expired</option>
                <option value="revoked">Revoked</option>
              </select>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  License Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Activations
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLicenses && filteredLicenses.length > 0 ? (
                filteredLicenses.map((license) => (
                  <tr key={license.id}>
                    <td className="px-6 py-4 whitespace-nowrap font-mono text-sm">
                      {license.licenseKey}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getProductName(license.productId)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {license.userId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {license.currentActivations} / {license.maxActivations}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        license.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : license.status === 'expired'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {license.status.charAt(0).toUpperCase() + license.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        href={`/admin/merchants/digital/${id}/licenses/${license.id}/edit`}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        Edit
                      </Link>
                      {license.status === 'active' && (
                        <button
                          onClick={() => handleRevokeLicense(license.id!)}
                          disabled={isRevoking}
                          className="text-red-600 hover:text-red-900"
                        >
                          Revoke
                        </button>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                    No licenses found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
