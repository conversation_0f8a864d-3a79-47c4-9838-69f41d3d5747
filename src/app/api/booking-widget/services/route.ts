import { NextRequest, NextResponse } from 'next/server';
import { bookingWidgetService } from '@/services/bookingWidgetService';

/**
 * GET handler for fetching available services for a booking widget
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const widgetId = searchParams.get('widgetId');
    const apiKey = searchParams.get('apiKey');
    
    if (!widgetId && !apiKey) {
      return NextResponse.json(
        { error: 'Widget ID or API key is required' },
        { status: 400 }
      );
    }
    
    // Get the origin of the request
    const origin = request.headers.get('origin') || '';
    
    // If API key is provided, validate it first
    if (apiKey) {
      try {
        const widget = await bookingWidgetService.validateApiKey(apiKey, origin);
        
        // Get available services
        const result = await bookingWidgetService.getAvailableServices(widget.id);
        
        return NextResponse.json(result);
      } catch (error) {
        return NextResponse.json(
          { error: error instanceof Error ? error.message : 'Invalid API key' },
          { status: 401 }
        );
      }
    }
    
    // If widget ID is provided, get available services directly
    if (widgetId) {
      const result = await bookingWidgetService.getAvailableServices(widgetId);
      
      return NextResponse.json(result);
    }
  } catch (error) {
    console.error('Error in GET /api/booking-widget/services:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
