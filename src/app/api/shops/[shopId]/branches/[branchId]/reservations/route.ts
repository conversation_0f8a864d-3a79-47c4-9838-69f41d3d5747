/**
 * API route for shop branch reservations management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/shops/${shopId}/branches/${branchId}/reservations${queryString ? `?${queryString}` : ''}`;

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/shops/[shopId]/branches/[branchId]/reservations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch reservations' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    const body = await request.json();

    // Transform frontend camelCase to backend snake_case
    const transformedBody = {
      customer_name: body.customerName,
      customer_phone: body.customerPhone,
      customer_email: body.customerEmail || '',
      party_size: body.partySize,
      reservation_date: body.date,
      reservation_time: body.time,
      duration: body.duration || 120,
      table_id: body.tableId || null,
      special_requests: body.specialRequests || '',
      notes: body.notes || '',
      source: body.source || 'website',
    };

    const response = await serverFetchClient(`/shops/${shopId}/branches/${branchId}/reservations`, request, {
      method: 'POST',
      body: JSON.stringify(transformedBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/shops/[shopId]/branches/[branchId]/reservations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create reservation' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
