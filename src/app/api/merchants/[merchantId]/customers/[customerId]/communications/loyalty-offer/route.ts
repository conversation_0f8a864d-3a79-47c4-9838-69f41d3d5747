import { NextRequest, NextResponse } from 'next/server';
import { customerCommunicationService } from '@/services';
import { z } from 'zod';
import { supabase } from '@/lib/supabase/client';

// Schema for sending a loyalty offer
const sendLoyaltyOfferSchema = z.object({
  type: z.enum(['email', 'sms']).default('email'),
  offerType: z.enum(['discount', 'free_service', 'gift']).default('discount'),
});

/**
 * POST /api/merchants/[merchantId]/customers/[customerId]/communications/loyalty-offer
 * Send a loyalty offer to a customer
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; customerId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = sendLoyaltyOfferSchema.parse(body);

    // Send loyalty offer
    const communication = await customerCommunicationService.sendLoyaltyOffer(
      params.merchantId,
      params.customerId,
      validatedData.type,
      validatedData.offerType
    );

    return NextResponse.json(communication);
  } catch (error) {
    console.error('Error sending loyalty offer:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to send loyalty offer', message: error.message },
      { status: 500 }
    );
  }
}
