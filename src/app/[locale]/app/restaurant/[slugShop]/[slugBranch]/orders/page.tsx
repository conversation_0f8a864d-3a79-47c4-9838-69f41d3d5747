'use client';

import React, { use, useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ArrowLeft, Search, Clock, CheckCircle, XCircle, Eye, RefreshCw, Filter, ArrowUpDown, ArrowUp, ArrowDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { useOrders } from '@/hooks/useOrders';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { formatCurrency } from '@/lib/utils';

interface OrdersPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function OrdersPage({ params }: OrdersPageProps) {
  const { slugShop, slugBranch } = use(params);
  const [searchInput, setSearchInput] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  const shopId = merchant?.id;
  const branchId = branch?.id;

  // Backend-driven orders management
  const {
    orders,
    pagination,
    orderStats,
    filters,
    isLoading,
    isError,
    error,
    updateFilters,
    clearFilters,
    setSearch,
    setStatus,
    setOrderType,
    setSorting,
    setPage,
    setLimit,
    refetch,
    getActiveOrders,
    getCompletedOrders,
    getCancelledOrders,
  } = useOrders({
    shopId: shopId || '',
    branchId: branchId || '',
    initialFilters: {
      page: 1,
      limit: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
      ...(activeTab !== 'all' && { status: activeTab as any })
    }
  });

  // Update search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      updateFilters({
        search: searchInput || undefined,
        page: 1
      });
    }, 300);
    return () => clearTimeout(timer);
  }, [searchInput]);

  // Update status filter when tab changes
  useEffect(() => {
    if (activeTab === 'all') {
      updateFilters({ status: undefined, page: 1 });
    } else {
      updateFilters({ status: activeTab as any, page: 1 });
    }
  }, [activeTab]);

  if (isLoadingMerchants || isLoading) {
    return <AppLoading />;
  }

  if (!merchant || !branch) {
    return (
      <div className="p-6 font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'preparing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ready':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get filtered orders based on current tab
  const getFilteredOrders = () => {
    switch (activeTab) {
      case 'pending':
        return orders.filter(order => order.status === 'pending');
      case 'preparing':
        return orders.filter(order => order.status === 'preparing');
      case 'ready':
        return orders.filter(order => order.status === 'ready');
      case 'completed':
        return orders.filter(order => ['completed', 'delivered'].includes(order.status));
      case 'cancelled':
        return orders.filter(order => order.status === 'cancelled');
      default:
        return orders;
    }
  };

  const filteredOrders = getFilteredOrders();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'preparing':
        return <Clock className="h-4 w-4" />;
      case 'ready':
        return <CheckCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <XCircle className="h-4 w-4" />;
    }
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const orderTime = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Orders Management</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage orders for your restaurant
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={clearFilters}
            className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
          >
            <Filter className="h-4 w-4 mr-2" />
            Clear Filters
          </Button>
          <Button
            variant="outline"
            onClick={refetch}
            className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#8a745c] text-sm font-medium">Total Orders</p>
                <p className="text-[#181510] text-2xl font-bold">{orderStats.totalOrders}</p>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Eye className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#8a745c] text-sm font-medium">Active Orders</p>
                <p className="text-[#181510] text-2xl font-bold">{orderStats.activeOrders}</p>
              </div>
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#8a745c] text-sm font-medium">Completed Orders</p>
                <p className="text-[#181510] text-2xl font-bold">{orderStats.completedOrders}</p>
              </div>
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#8a745c] text-sm font-medium">Total Revenue</p>
                <p className="text-[#181510] text-2xl font-bold">{formatCurrency(orderStats.totalRevenue)}</p>
              </div>
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col lg:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
            <Input
              placeholder="Search orders by ID, customer name, or items..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Select value={filters.status || 'all'} onValueChange={(value) => setStatus(value === 'all' ? undefined : value as any)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="preparing">Preparing</SelectItem>
              <SelectItem value="ready">Ready</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.order_type || 'all'} onValueChange={(value) => setOrderType(value === 'all' ? undefined : value as any)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Order Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="dine-in">Dine In</SelectItem>
              <SelectItem value="takeout">Takeout</SelectItem>
              <SelectItem value="delivery">Delivery</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList className="bg-[#f1edea] p-1">
            <TabsTrigger value="all" className="data-[state=active]:bg-white">
              All Orders ({pagination?.totalItems || 0})
            </TabsTrigger>
            <TabsTrigger value="pending" className="data-[state=active]:bg-white">
              Pending ({orderStats.activeOrders})
            </TabsTrigger>
            <TabsTrigger value="preparing" className="data-[state=active]:bg-white">
              Preparing
            </TabsTrigger>
            <TabsTrigger value="ready" className="data-[state=active]:bg-white">
              Ready
            </TabsTrigger>
            <TabsTrigger value="completed" className="data-[state=active]:bg-white">
              Completed ({orderStats.completedOrders})
            </TabsTrigger>
            <TabsTrigger value="cancelled" className="data-[state=active]:bg-white">
              Cancelled ({orderStats.cancelledOrders})
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Orders Table */}
        <TabsContent value={activeTab} className="mt-0">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-0">
              {orders.length === 0 ? (
                <div className="flex items-center justify-center h-64 text-[#8a745c]">
                  {filters.search ? 'No orders match your search criteria' : 'No orders found'}
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow className="border-[#e5e1dc]">
                        <TableHead
                          className="cursor-pointer hover:bg-[#f1edea] transition-colors"
                          onClick={() => setSorting('order_number', filters.sort_order === 'asc' ? 'desc' : 'asc')}
                        >
                          <div className="flex items-center gap-1">
                            Order ID
                            {filters.sort_by === 'order_number' ? (
                              filters.sort_order === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                            ) : (
                              <ArrowUpDown className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-[#f1edea] transition-colors"
                          onClick={() => setSorting('customer_name', filters.sort_order === 'asc' ? 'desc' : 'asc')}
                        >
                          <div className="flex items-center gap-1">
                            Customer
                            {filters.sort_by === 'customer_name' ? (
                              filters.sort_order === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                            ) : (
                              <ArrowUpDown className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-[#f1edea] transition-colors"
                          onClick={() => setSorting('status', filters.sort_order === 'asc' ? 'desc' : 'asc')}
                        >
                          <div className="flex items-center gap-1">
                            Status
                            {filters.sort_by === 'status' ? (
                              filters.sort_order === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                            ) : (
                              <ArrowUpDown className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-[#f1edea] transition-colors"
                          onClick={() => setSorting('total_amount', filters.sort_order === 'asc' ? 'desc' : 'asc')}
                        >
                          <div className="flex items-center gap-1">
                            Total
                            {filters.sort_by === 'total_amount' ? (
                              filters.sort_order === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                            ) : (
                              <ArrowUpDown className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-[#f1edea] transition-colors"
                          onClick={() => setSorting('created_at', filters.sort_order === 'asc' ? 'desc' : 'asc')}
                        >
                          <div className="flex items-center gap-1">
                            Created
                            {filters.sort_by === 'created_at' ? (
                              filters.sort_order === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                            ) : (
                              <ArrowUpDown className="h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {orders.map((order) => (
                        <TableRow key={order.id} className="border-[#e5e1dc] hover:bg-[#f8f6f3]">
                          <TableCell className="font-medium text-[#181510]">
                            {order.orderNumber || order.id}
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="text-[#181510] font-medium">{order.customerName}</p>
                              <p className="text-[#8a745c] text-sm">{order.customerPhone}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4] capitalize">
                              {order.type || 'dine-in'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(order.status)}>
                              {getStatusIcon(order.status)}
                              <span className="ml-1 capitalize">{order.status}</span>
                            </Badge>
                          </TableCell>
                          <TableCell className="font-medium text-[#181510]">
                            {formatCurrency(order.total)}
                          </TableCell>
                          <TableCell className="text-[#8a745c]">
                            {getTimeAgo(order.createdAt)}
                          </TableCell>
                          <TableCell>
                            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders/${order.id.replace('#', '')}`}>
                              <Button variant="outline" size="sm" className="border-[#e2dcd4]">
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                            </Link>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  {pagination && pagination.totalPages > 1 && (
                    <div className="flex items-center justify-between px-6 py-4 border-t border-[#e5e1dc]">
                      <div className="text-sm text-[#8a745c]">
                        Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
                        {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
                        {pagination.totalItems} orders
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPage(pagination.currentPage - 1)}
                          disabled={!pagination.hasPreviousPage}
                          className="border-[#e2dcd4]"
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>
                        <span className="text-sm text-[#8a745c]">
                          Page {pagination.currentPage} of {pagination.totalPages}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPage(pagination.currentPage + 1)}
                          disabled={!pagination.hasNextPage}
                          className="border-[#e2dcd4]"
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>


      </Tabs>
    </div>
  );
}
