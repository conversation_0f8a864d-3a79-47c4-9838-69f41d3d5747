package database

import (
	"errors"
	"time"

	"restaurant-backend/internal/models"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize initializes the database connection
func Initialize(dsn string, log *logrus.Logger) (*gorm.DB, error) {
	// Determine GORM log level based on application log level
	var gormLogLevel logger.LogLevel
	switch log.Level {
	case logrus.DebugLevel:
		gormLogLevel = logger.Info // Show all queries in debug mode
	case logrus.InfoLevel:
		gormLogLevel = logger.Warn // Only show warnings and errors in info mode
	case logrus.WarnLevel:
		gormLogLevel = logger.Error // Only show errors in warn mode
	case logrus.ErrorLevel, logrus.FatalLevel, logrus.PanicLevel:
		gormLogLevel = logger.Silent // Silent in error/fatal/panic modes
	default:
		gormLogLevel = logger.Warn // Default to warn level
	}

	// Configure GORM logger
	gormLogger := logger.New(
		log,
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  gormLogLevel,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// Open database connection
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, err
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, err
	}

	log.Info("Database connection established successfully")
	return db, nil
}

// Migrate runs database migrations
func Migrate(db *gorm.DB) error {
	// Enable UUID extension
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		return err
	}

	// Fix data integrity issues before migration
	if err := fixDataIntegrity(db); err != nil {
		return err
	}

	// Auto-migrate all models
	err := db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.MenuCategory{},
		&models.MenuItem{},
		&models.Order{},
		&models.OrderItem{},
		&models.Payment{},
		&models.Reservation{},
		&models.Table{},
		&models.TableArea{},
		&models.Floor{},
		&models.Review{},

		// New models for services, shops, and campaigns
		&models.Shop{},
		&models.ShopBranch{},
		&models.Service{},
		&models.Staff{},
		&models.Appointment{},
		&models.StaffService{},
		&models.ServiceAvailability{},
		&models.CommunicationTemplate{},
		&models.CampaignSegment{},
		&models.CommunicationCampaign{},
		&models.CommunicationAnalytics{},
	)
	if err != nil {
		return err
	}

	// Create indexes for better performance
	if err := createIndexes(db); err != nil {
		return err
	}

	// Seed default data
	if err := seedDefaultData(db); err != nil {
		return err
	}

	// Create default floors and areas for existing branches
	if err := createDefaultFloorsAndAreas(db); err != nil {
		return err
	}

	return nil
}

// fixDataIntegrity fixes data integrity issues before running migrations
func fixDataIntegrity(db *gorm.DB) error {
	// Check if shops table exists
	if !db.Migrator().HasTable(&models.Shop{}) {
		return nil // Table doesn't exist yet, no need to fix
	}

	// Check if users table exists
	if !db.Migrator().HasTable(&models.User{}) {
		return nil // Users table doesn't exist yet, no need to fix
	}

	// Check if there are any orphaned shops
	var orphanedCount int64
	err := db.Raw(`
		SELECT COUNT(*)
		FROM shops s
		LEFT JOIN users u ON s.owner_id = u.id
		WHERE u.id IS NULL AND s.owner_id IS NOT NULL
	`).Scan(&orphanedCount).Error
	if err != nil {
		return err
	}

	// Check for shops with NULL owner_id
	var nullOwnerCount int64
	err = db.Raw(`
		SELECT COUNT(*)
		FROM shops s
		WHERE s.owner_id IS NULL
	`).Scan(&nullOwnerCount).Error
	if err != nil {
		return err
	}

	// If no orphaned shops, no need to fix
	if orphanedCount == 0 && nullOwnerCount == 0 {
		return nil
	}

	// Get the first existing user to use as system owner
	var firstUser models.User
	result := db.First(&firstUser)
	if result.Error != nil {
		return result.Error // No users exist, cannot fix
	}

	// Update orphaned shops to use the first existing user
	if orphanedCount > 0 {
		updateQuery := `
			UPDATE shops
			SET owner_id = ?, updated_at = NOW()
			WHERE owner_id IS NOT NULL
			AND owner_id NOT IN (SELECT id FROM users)
		`

		if err := db.Exec(updateQuery, firstUser.ID).Error; err != nil {
			return err
		}
	}

	// Handle shops with NULL owner_id
	if nullOwnerCount > 0 {
		nullOwnerQuery := `
			UPDATE shops
			SET owner_id = ?, updated_at = NOW()
			WHERE owner_id IS NULL
		`

		if err := db.Exec(nullOwnerQuery, firstUser.ID).Error; err != nil {
			return err
		}
	}

	return nil
}

// createIndexes creates additional database indexes for performance
func createIndexes(db *gorm.DB) error {
	indexes := []string{
		// Orders indexes
		"CREATE INDEX IF NOT EXISTS idx_orders_branch_status ON orders(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_orders_customer_phone ON orders(customer_phone)",

		// Menu items indexes
		"CREATE INDEX IF NOT EXISTS idx_menu_items_branch_category ON menu_items(branch_id, category_id)",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_available ON menu_items(branch_id) WHERE is_available = true",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_slug ON menu_items(branch_id, slug)",

		// Reservations indexes
		"CREATE INDEX IF NOT EXISTS idx_reservations_branch_date ON reservations(branch_id, reservation_date)",
		"CREATE INDEX IF NOT EXISTS idx_reservations_status ON reservations(branch_id, status)",

		// Reviews indexes
		"CREATE INDEX IF NOT EXISTS idx_reviews_branch_rating ON reviews(branch_id, rating)",
		"CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at)",

		// Tables indexes
		"CREATE INDEX IF NOT EXISTS idx_tables_branch_status ON tables(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_tables_active ON tables(branch_id) WHERE is_active = true",

		// Users indexes
		"CREATE INDEX IF NOT EXISTS idx_users_branch_status ON users(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_users_role ON users(role_id)",
	}

	for _, index := range indexes {
		if err := db.Exec(index).Error; err != nil {
			return err
		}
	}

	return nil
}

// seedDefaultData seeds the database with default data
func seedDefaultData(db *gorm.DB) error {
	// Check if we already have shop data
	var shopCount int64
	db.Model(&models.Shop{}).Count(&shopCount)

	// If we have shops, skip seeding
	if shopCount > 0 {
		return nil // Data already exists
	}

	// Create default permissions
	if err := seedDefaultRoles(db); err != nil {
		return err
	}

	// Create demo shop and branch data
	if err := seedDemoData(db); err != nil {
		return err
	}

	return nil
}

// seedDefaultRoles creates default roles and permissions
func seedDefaultRoles(db *gorm.DB) error {
	// This will be implemented when we create the demo data
	return nil
}

// createDefaultFloorsAndAreas creates default floor and area for branches that don't have them
func createDefaultFloorsAndAreas(db *gorm.DB) error {
	// Get all branches that don't have floors
	var branches []models.ShopBranch
	err := db.Raw(`
		SELECT sb.* FROM shop_branches sb
		LEFT JOIN floors f ON sb.id = f.branch_id AND f.is_active = true
		WHERE f.id IS NULL
	`).Scan(&branches).Error
	if err != nil {
		return err
	}

	for _, branch := range branches {
		// Create default floor
		floor := models.Floor{
			BranchID:    branch.ID,
			Name:        "Ground Floor",
			Description: "Main dining area",
			Order:       1,
			Layout: models.FloorLayout{
				Width:    800,
				Height:   600,
				GridSize: 20,
				ShowGrid: true,
			},
			IsActive: true,
		}

		if err := db.Create(&floor).Error; err != nil {
			return err
		}

		// Create default area
		area := models.TableArea{
			BranchID:    branch.ID,
			FloorID:     &floor.ID,
			Name:        "Main Dining Area",
			Description: "Primary seating area",
			Color:       "#8a745c",
			IsActive:    true,
		}

		if err := db.Create(&area).Error; err != nil {
			return err
		}
	}

	return nil
}

// seedDemoData creates demo shop and sample data
func seedDemoData(db *gorm.DB) error {
	// Create the shop data that the frontend expects
	if err := seedShopData(db); err != nil {
		return err
	}

	// Get the created shop and branch for further seeding
	var shop models.Shop
	if err := db.Where("slug = ?", "thai-delight").First(&shop).Error; err != nil {
		return err
	}

	var branch models.ShopBranch
	if err := db.Where("shop_id = ? AND slug = ?", shop.ID, "downtown").First(&branch).Error; err != nil {
		return err
	}

	// Create default admin role
	adminRole := &models.Role{
		MerchantID:  shop.ID,
		Name:        "Administrator",
		Description: "Full access to all features",
		Permissions: models.PermissionsData{
			models.PermissionViewDashboard,
			models.PermissionManageOrders,
			models.PermissionManageMenu,
			models.PermissionManageStaff,
			models.PermissionManageReservations,
			models.PermissionManageTables,
			models.PermissionManageReviews,
			models.PermissionViewReports,
			models.PermissionManageSettings,
		},
		IsActive: true,
	}

	if err := db.Create(adminRole).Error; err != nil {
		return err
	}

	// Create demo admin user
	adminUser := &models.User{
		MerchantID: shop.ID,
		BranchID:   &branch.ID,
		Email:      "<EMAIL>",
		FirstName:  "Admin",
		LastName:   "User",
		Phone:      "+66 2 123 4567",
		RoleID:     adminRole.ID,
		Position:   "General Manager",
		Department: "Management",
		EmployeeID: "EMP001",
		Status:     models.UserStatusActive,
		HireDate:   time.Now().AddDate(-1, 0, 0), // Hired 1 year ago
	}

	// Set default password
	if err := adminUser.SetPassword("admin123"); err != nil {
		return err
	}

	if err := db.Create(adminUser).Error; err != nil {
		return err
	}

	// Create sample menu categories
	categories := []*models.MenuCategory{
		{
			BranchID:    branch.ID,
			Name:        "Appetizers",
			Slug:        "appetizers",
			Description: "Start your meal with our delicious appetizers",
			SortOrder:   1,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Main Courses",
			Slug:        "main-courses",
			Description: "Our signature main dishes",
			SortOrder:   2,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Desserts",
			Slug:        "desserts",
			Description: "Sweet endings to your meal",
			SortOrder:   3,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Beverages",
			Slug:        "beverages",
			Description: "Refreshing drinks and beverages",
			SortOrder:   4,
			IsActive:    true,
		},
	}

	for _, category := range categories {
		if err := db.Create(category).Error; err != nil {
			return err
		}
	}

	// Create sample menu items
	menuItems := []*models.MenuItem{
		{
			BranchID:        branch.ID,
			CategoryID:      &categories[0].ID, // Appetizers
			Name:            "Caesar Salad",
			Slug:            "caesar-salad",
			Description:     "Fresh romaine lettuce with parmesan cheese and croutons",
			Price:           12.99,
			PreparationTime: intPtr(10),
			IsAvailable:     true,
			IsVegetarian:    true,
			Tags:            models.TagsData{"popular", "healthy"},
		},
		{
			BranchID:        branch.ID,
			CategoryID:      &categories[1].ID, // Main Courses
			Name:            "Grilled Salmon",
			Slug:            "grilled-salmon",
			Description:     "Fresh Atlantic salmon grilled to perfection",
			Price:           24.99,
			PreparationTime: intPtr(20),
			IsAvailable:     true,
			IsGlutenFree:    true,
			Tags:            models.TagsData{"signature", "healthy"},
		},
		{
			BranchID:        branch.ID,
			CategoryID:      &categories[2].ID, // Desserts
			Name:            "Chocolate Cake",
			Slug:            "chocolate-cake",
			Description:     "Rich chocolate cake with vanilla ice cream",
			Price:           8.99,
			PreparationTime: intPtr(5),
			IsAvailable:     true,
			IsVegetarian:    true,
			Tags:            models.TagsData{"popular", "sweet"},
		},
	}

	for _, item := range menuItems {
		if err := db.Create(item).Error; err != nil {
			return err
		}
	}

	// Create sample table areas
	areas := []*models.TableArea{
		{
			BranchID:    branch.ID,
			Name:        "Dining Area",
			Description: "Main dining room",
			Color:       "#8a745c",
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Outdoor Patio",
			Description: "Outdoor seating area",
			Color:       "#6b8e23",
			IsActive:    true,
		},
	}

	for _, area := range areas {
		if err := db.Create(area).Error; err != nil {
			return err
		}
	}

	// Get the created areas for table creation
	var diningArea, patioArea models.TableArea
	if err := db.Where("branch_id = ? AND name = ?", branch.ID, "Dining Area").First(&diningArea).Error; err != nil {
		return err
	}
	if err := db.Where("branch_id = ? AND name = ?", branch.ID, "Outdoor Patio").First(&patioArea).Error; err != nil {
		return err
	}

	// Create sample tables
	tables := []*models.Table{
		// Dining Area Tables
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 1",
			Number:   1,
			Capacity: 2,
			Status:   "available",
			Position: models.PositionData{X: 100, Y: 100},
			Shape:    "square",
			Size:     models.SizeData{Width: 80, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 2",
			Number:   2,
			Capacity: 4,
			Status:   "occupied",
			Position: models.PositionData{X: 200, Y: 100},
			Shape:    "round",
			Size:     models.SizeData{Width: 100, Height: 100},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 3",
			Number:   3,
			Capacity: 6,
			Status:   "reserved",
			Position: models.PositionData{X: 300, Y: 100},
			Shape:    "rectangle",
			Size:     models.SizeData{Width: 120, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 4",
			Number:   4,
			Capacity: 4,
			Status:   "available",
			Position: models.PositionData{X: 100, Y: 200},
			Shape:    "square",
			Size:     models.SizeData{Width: 80, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 5",
			Number:   5,
			Capacity: 2,
			Status:   "cleaning",
			Position: models.PositionData{X: 200, Y: 200},
			Shape:    "round",
			Size:     models.SizeData{Width: 80, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 6",
			Number:   6,
			Capacity: 8,
			Status:   "available",
			Position: models.PositionData{X: 300, Y: 200},
			Shape:    "rectangle",
			Size:     models.SizeData{Width: 140, Height: 100},
			IsActive: true,
		},
		// Outdoor Patio Tables
		{
			BranchID: branch.ID,
			AreaID:   &patioArea.ID,
			Name:     "Patio Table 1",
			Number:   7,
			Capacity: 4,
			Status:   "available",
			Position: models.PositionData{X: 100, Y: 100},
			Shape:    "round",
			Size:     models.SizeData{Width: 100, Height: 100},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &patioArea.ID,
			Name:     "Patio Table 2",
			Number:   8,
			Capacity: 6,
			Status:   "occupied",
			Position: models.PositionData{X: 250, Y: 100},
			Shape:    "rectangle",
			Size:     models.SizeData{Width: 120, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &patioArea.ID,
			Name:     "Patio Table 3",
			Number:   9,
			Capacity: 2,
			Status:   "available",
			Position: models.PositionData{X: 100, Y: 200},
			Shape:    "square",
			Size:     models.SizeData{Width: 80, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &patioArea.ID,
			Name:     "Patio Table 4",
			Number:   10,
			Capacity: 4,
			Status:   "reserved",
			Position: models.PositionData{X: 250, Y: 200},
			Shape:    "round",
			Size:     models.SizeData{Width: 100, Height: 100},
			IsActive: true,
		},
	}

	for _, table := range tables {
		if err := db.Create(table).Error; err != nil {
			return err
		}
	}

	// Create sample reservations
	reservations := []*models.Reservation{
		{
			BranchID:        branch.ID,
			CustomerName:    "John Smith",
			CustomerPhone:   "+1234567890",
			CustomerEmail:   "<EMAIL>",
			PartySize:       4,
			ReservationDate: time.Now().AddDate(0, 0, 0), // Today
			ReservationTime: time.Now().Add(2 * time.Hour),
			Duration:        120,
			TableID:         &tables[1].ID, // Table 2
			Status:          "confirmed",
			SpecialRequests: "Window seat preferred",
			Source:          "website",
		},
		{
			BranchID:        branch.ID,
			CustomerName:    "Sarah Johnson",
			CustomerPhone:   "+1234567891",
			CustomerEmail:   "<EMAIL>",
			PartySize:       2,
			ReservationDate: time.Now().AddDate(0, 0, 0), // Today
			ReservationTime: time.Now().Add(4 * time.Hour),
			Duration:        90,
			TableID:         &tables[0].ID, // Table 1
			Status:          "confirmed",
			SpecialRequests: "Anniversary dinner",
			Source:          "phone",
		},
		{
			BranchID:        branch.ID,
			CustomerName:    "Mike Wilson",
			CustomerPhone:   "+1234567892",
			CustomerEmail:   "<EMAIL>",
			PartySize:       6,
			ReservationDate: time.Now().AddDate(0, 0, 1), // Tomorrow
			ReservationTime: time.Now().AddDate(0, 0, 1).Add(6 * time.Hour),
			Duration:        150,
			TableID:         &tables[2].ID, // Table 3
			Status:          "pending",
			SpecialRequests: "Birthday celebration",
			Source:          "app",
		},
		{
			BranchID:        branch.ID,
			CustomerName:    "Emily Davis",
			CustomerPhone:   "+1234567893",
			CustomerEmail:   "<EMAIL>",
			PartySize:       4,
			ReservationDate: time.Now().AddDate(0, 0, 1), // Tomorrow
			ReservationTime: time.Now().AddDate(0, 0, 1).Add(7 * time.Hour),
			Duration:        120,
			TableID:         &tables[6].ID, // Patio Table 1
			Status:          "confirmed",
			SpecialRequests: "Outdoor seating",
			Source:          "website",
		},
	}

	for _, reservation := range reservations {
		if err := db.Create(reservation).Error; err != nil {
			return err
		}
	}

	return nil
}

// Helper function to create int pointer
func intPtr(i int) *int {
	return &i
}

// InitializeTest initializes an in-memory SQLite database for testing
func InitializeTest() (*gorm.DB, error) {
	// For testing, we'll use SQLite in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return nil, err
	}

	return db, nil
}

// MigrateTest runs database migrations for testing (without PostgreSQL extensions)
func MigrateTest(db *gorm.DB) error {
	// Auto-migrate all models (SQLite doesn't need UUID extension)
	err := db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.MenuCategory{},
		&models.MenuItem{},
		&models.Order{},
		&models.OrderItem{},
		&models.Payment{},
		&models.Reservation{},
		&models.Table{},
		&models.TableArea{},
		&models.Floor{},
		&models.Review{},

		// New models for services, shops, and campaigns
		&models.Shop{},
		&models.ShopBranch{},
		&models.Service{},
		&models.Staff{},
		&models.Appointment{},
		&models.StaffService{},
		&models.ServiceAvailability{},
		&models.CommunicationTemplate{},
		&models.CampaignSegment{},
		&models.CommunicationCampaign{},
		&models.CommunicationAnalytics{},
	)
	if err != nil {
		return err
	}

	// Create basic indexes (SQLite compatible)
	if err := createTestIndexes(db); err != nil {
		return err
	}

	return nil
}

// createTestIndexes creates basic database indexes for testing
func createTestIndexes(db *gorm.DB) error {
	indexes := []string{
		// Basic indexes that work with SQLite
		"CREATE INDEX IF NOT EXISTS idx_orders_branch_status ON orders(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_branch_category ON menu_items(branch_id, category_id)",
		"CREATE INDEX IF NOT EXISTS idx_reservations_branch_date ON reservations(branch_id, reservation_date)",
		"CREATE INDEX IF NOT EXISTS idx_reviews_branch_rating ON reviews(branch_id, rating)",
		"CREATE INDEX IF NOT EXISTS idx_tables_branch_status ON tables(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_users_branch_status ON users(branch_id, status)",
	}

	for _, index := range indexes {
		if err := db.Exec(index).Error; err != nil {
			return err
		}
	}

	return nil
}

// seedShopData creates shop data that matches the frontend expectations
func seedShopData(db *gorm.DB) error {
	// Check if shop data already exists
	var shopCount int64
	db.Model(&models.Shop{}).Where("slug = ?", "thai-delight").Count(&shopCount)

	// Check if branches exist
	var branchCount int64
	db.Model(&models.ShopBranch{}).
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Where("shops.slug = ?", "thai-delight").
		Count(&branchCount)

	if shopCount > 0 && branchCount > 0 {
		return nil // Shop and branches already exist
	}

	// Get or create a test user to own the shop
	var testUser models.User
	err := db.Where("email = ?", "<EMAIL>").First(&testUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new user
			testUser = models.User{
				Email:     "<EMAIL>",
				FirstName: "Test",
				LastName:  "User",
				Status:    models.UserStatusActive,
			}

			// Set password
			if err := testUser.SetPassword("password123"); err != nil {
				return err
			}

			if err := db.Create(&testUser).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Get or create Thai Delight shop
	var thaiDelightShop models.Shop
	err = db.Where("slug = ?", "thai-delight").First(&thaiDelightShop).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new shop
			thaiDelightShop = models.Shop{
				OwnerID:     testUser.ID,
				Name:        "Thai Delight",
				Slug:        "thai-delight",
				Description: "Authentic Thai cuisine in a cozy atmosphere",
				ShopType:    "restaurant",
				Email:       "<EMAIL>",
				Phone:       "+66 2 123 4567",
				Website:     "https://thaidelight.com",
				Logo:        "https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&w=1974&auto=format&fit=crop",
				Address: models.Address{
					Street:  "123 Thai Street",
					City:    "Bangkok",
					State:   "Bangkok",
					ZipCode: "10110",
					Country: "Thailand",
				},
				CuisineType: "Thai",
				PriceRange:  "$$",
				Rating:      4.5,
				ReviewCount: 128,
				BusinessHours: map[string]string{
					"monday":    "10:00-22:00",
					"tuesday":   "10:00-22:00",
					"wednesday": "10:00-22:00",
					"thursday":  "10:00-22:00",
					"friday":    "10:00-23:00",
					"saturday":  "11:00-23:00",
					"sunday":    "11:00-22:00",
				},
				Status:     "active",
				IsVerified: true,
				IsActive:   true,
			}

			if err := db.Create(&thaiDelightShop).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Create or get Downtown branch
	var downtownBranch models.ShopBranch
	err = db.Where("shop_id = ? AND slug = ?", thaiDelightShop.ID, "downtown").First(&downtownBranch).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new branch
			downtownBranch = models.ShopBranch{
				ShopID: thaiDelightShop.ID,
				Name:   "Downtown",
				Slug:   "downtown",
				Email:  "<EMAIL>",
				Phone:  "+66 2 123 4567",
				Address: models.Address{
					Street:  "123 Main Street",
					City:    "Bangkok",
					State:   "Bangkok",
					ZipCode: "10110",
					Country: "Thailand",
				},
				BusinessHours: map[string]string{
					"monday":    "10:00-22:00",
					"tuesday":   "10:00-22:00",
					"wednesday": "10:00-22:00",
					"thursday":  "10:00-22:00",
					"friday":    "10:00-23:00",
					"saturday":  "11:00-23:00",
					"sunday":    "11:00-22:00",
				},
				Timezone: "Asia/Bangkok",
				Status:   "active",
				IsActive: true,
			}

			if err := db.Create(&downtownBranch).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Create or get Riverside branch
	var riversideBranch models.ShopBranch
	err = db.Where("shop_id = ? AND slug = ?", thaiDelightShop.ID, "riverside").First(&riversideBranch).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new branch
			riversideBranch = models.ShopBranch{
				ShopID: thaiDelightShop.ID,
				Name:   "Riverside",
				Slug:   "riverside",
				Email:  "<EMAIL>",
				Phone:  "+66 2 234 5678",
				Address: models.Address{
					Street:  "456 River Road",
					City:    "Bangkok",
					State:   "Bangkok",
					ZipCode: "10120",
					Country: "Thailand",
				},
				BusinessHours: map[string]string{
					"monday":    "10:00-22:00",
					"tuesday":   "10:00-22:00",
					"wednesday": "10:00-22:00",
					"thursday":  "10:00-22:00",
					"friday":    "10:00-23:00",
					"saturday":  "11:00-23:00",
					"sunday":    "11:00-22:00",
				},
				Timezone: "Asia/Bangkok",
				Status:   "active",
				IsActive: true,
			}

			if err := db.Create(&riversideBranch).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	return nil
}
