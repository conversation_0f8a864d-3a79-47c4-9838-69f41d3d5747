'use client';

import React, { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { AppLoading } from '@/components/ui/app-loading';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { TableImage } from '@/components/ui/image-with-fallback';

import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetTablesQuery, useGetTableAreasQuery, Table, TableArea } from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import { useGetReservationsQuery, Reservation } from '@/lib/redux/api/endpoints/restaurant/reservationsApi';
import { getTableImageByNumber } from '@/data/sample-table-images';

interface TablesPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function TablesPage({ params }: TablesPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [activeTab, setActiveTab] = useState('floor-plan');

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants, error: merchantsError } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  // Get tables data from backend
  const {
    data: tablesData,
    isLoading: isLoadingTables,
    error: tablesError
  } = useGetTablesQuery(
    {
      shopId: merchant?.id || '',
      branchId: branch?.id || ''
    },
    {
      skip: !merchant?.id || !branch?.id
    }
  );

  // Get table areas from backend
  const {
    data: areasData,
    isLoading: isLoadingAreas,
    error: areasError
  } = useGetTableAreasQuery(
    {
      shopId: merchant?.id || '',
      branchId: branch?.id || ''
    },
    {
      skip: !merchant?.id || !branch?.id
    }
  );

  // Get reservations data from backend
  const {
    data: reservationsData,
    isLoading: isLoadingReservations,
    error: reservationsError
  } = useGetReservationsQuery(
    {
      shopId: merchant?.id || '',
      branchId: branch?.id || '',
      filters: { status: 'confirmed' }
    },
    {
      skip: !merchant?.id || !branch?.id
    }
  );

  const isLoading = isLoadingMerchants || isLoadingTables || isLoadingAreas || isLoadingReservations;
  const hasError = merchantsError || tablesError || areasError || reservationsError;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Error Loading Data</h1>
        <p className="text-[#8a745c] text-sm">There was an error loading the table data. Please try again.</p>
      </div>
    );
  }

  if (!merchant || !branch) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
        <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
      </div>
    );
  }

  // Use real data from backend or fallback to empty arrays
  // The Redux API transformResponse extracts data from paginated response
  const tables = tablesData || [];
  const areas = areasData || [];
  const reservations = reservationsData?.data || [];

  // Sample data for demonstration (when no real data is available)
  const sampleReservations = reservations.length === 0 ? [
    {
      id: '1',
      time: '7:00 PM',
      customerName: 'Sophia Clark',
      partySize: 4,
      tableName: 'Table 3',
      tableId: '3',
      status: 'confirmed'
    },
    {
      id: '2',
      time: '7:30 PM',
      customerName: 'Ethan Bennett',
      partySize: 2,
      tableName: 'Table 7',
      tableId: '7',
      status: 'confirmed'
    },
    {
      id: '3',
      time: '8:00 PM',
      customerName: 'Olivia Carter',
      partySize: 6,
      tableName: 'Table 11',
      tableId: '11',
      status: 'pending'
    },
    {
      id: '4',
      time: '8:30 PM',
      customerName: 'Liam Harper',
      partySize: 3,
      tableName: 'Table 2',
      tableId: '2',
      status: 'confirmed'
    },
    {
      id: '5',
      time: '9:00 PM',
      customerName: 'Ava Foster',
      partySize: 5,
      tableName: 'Table 9',
      tableId: '9',
      status: 'confirmed'
    }
  ] : reservations;

  // Group tables by area
  const tablesByArea: Record<string, Table[]> = areas.reduce((acc: Record<string, Table[]>, area: TableArea) => {
    // Match tables to areas by area ID or name
    acc[area.name] = tables.filter((table: Table) =>
      table.area?.id === area.id || table.area?.name === area.name
    );
    return acc;
  }, {});

  // If no areas, create a default area with all tables
  if (areas.length === 0 && tables.length > 0) {
    tablesByArea['Main Dining Area'] = tables;
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-black rounded flex items-center justify-center">
            <div className="w-4 h-4 bg-white rounded-sm"></div>
          </div>
          <h1 className="text-xl font-semibold text-gray-900">Table Manager</h1>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search"
              className="w-64 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <div className="absolute right-3 top-2.5">
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          <button className="p-2 text-gray-400 hover:text-gray-600">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-12" />
            </svg>
          </button>
          <div className="w-8 h-8 bg-orange-400 rounded-full"></div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Table Layout</h2>
          <p className="text-gray-600">Manage your restaurant's table layout and reservations.</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-8">
            <button
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'floor-plan'
                  ? 'border-gray-900 text-gray-900'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('floor-plan')}
            >
              Floor Plan
            </button>
            <button
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'reservations'
                  ? 'border-gray-900 text-gray-900'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('reservations')}
            >
              Reservations
            </button>
            <button
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'waitlist'
                  ? 'border-gray-900 text-gray-900'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('waitlist')}
            >
              Waitlist
            </button>
          </nav>
        </div>

      {activeTab === 'floor-plan' && (
        <div>
          {Object.entries(tablesByArea).map(([areaName, areaTables]) => (
            areaTables.length > 0 && (
              <div key={areaName} className="mb-8">
                <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
                  {areaName}
                </h2>
                <div className="grid grid-cols-[repeat(auto-fit,minmax(200px,1fr))] gap-4 p-4">
                  {areaTables.map((table: Table) => (
                    <Link key={table.id} href={`/app/restaurant/${slugShop}/${slugBranch}/tables/${table.id}`}>
                      <Card className="cursor-pointer hover:shadow-lg transition-shadow overflow-hidden h-[200px] relative">
                        {/* Table Image with Fallback */}
                        <TableImage
                          src={table.image_url || getTableImageByNumber(parseInt(table.number) || 1)}
                          alt={`${table.name} - Table ${table.number}`}
                          className="absolute inset-0"
                        />

                        {/* Overlay for better text readability */}
                        <div className="absolute inset-0 bg-black bg-opacity-30"></div>

                        <CardContent className="relative z-10 p-4 h-full flex flex-col justify-between">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="text-white text-lg font-bold drop-shadow-lg">{table.name}</h3>
                              <p className="text-white/90 text-sm drop-shadow">Table {table.number}</p>
                            </div>
                            <Badge
                              variant={
                                table.status === 'available' ? 'default' :
                                table.status === 'occupied' ? 'destructive' :
                                table.status === 'reserved' ? 'secondary' :
                                'outline'
                              }
                              className="text-xs bg-white/90 text-[#181510] border-0"
                            >
                              {table.status}
                            </Badge>
                          </div>

                          <div className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="text-white/90 drop-shadow">Capacity:</span>
                              <span className="text-white font-medium drop-shadow">{table.capacity} people</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-white/90 drop-shadow">Shape:</span>
                              <span className="text-white font-medium capitalize drop-shadow">{table.shape}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            )
          ))}

          {/* Show message when no real data */}
          {tables.length === 0 && (
            <div className="text-center py-12">
              <p className="text-[#8a745c] text-lg mb-4">No tables found</p>
              <p className="text-[#8a745c] text-sm">Add tables to get started with your restaurant layout.</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 px-4 py-6">
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/layout-editor`}>
              <Button variant="outline" className="bg-[#f1edea] text-[#181510] border-[#e2dcd4] hover:bg-[#e2dcd4]">
                Edit Layout
              </Button>
            </Link>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/new`}>
              <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d4b896]">
                Add Reservation
              </Button>
            </Link>
          </div>
        </div>
      )}

        {activeTab === 'reservations' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Upcoming Reservations</h3>
              <div className="flex gap-3">
                <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/layout-editor`}>
                  <Button variant="outline" className="text-gray-700 border-gray-300 hover:bg-gray-50">
                    Edit Layout
                  </Button>
                </Link>
                <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/new`}>
                  <Button className="bg-orange-500 text-white hover:bg-orange-600">
                    Add Reservation
                  </Button>
                </Link>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Party Size</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Table</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sampleReservations && sampleReservations.length > 0 ? (
                    sampleReservations.map((reservation: any) => (
                      <tr key={reservation.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {reservation.time}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {reservation.customerName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {reservation.partySize}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {reservation.tableName || `Table ${reservation.tableId}`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            reservation.status === 'confirmed'
                              ? 'bg-green-100 text-green-800'
                              : reservation.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {reservation.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <Link
                            href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/${reservation.id}`}
                            className="text-gray-600 hover:text-gray-900 font-medium"
                          >
                            View
                          </Link>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                        No reservations found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

      {activeTab === 'waitlist' && (
        <div className="mt-6">
          <div className="p-8 text-center text-[#8a745c]">
            <p>Waitlist feature coming soon</p>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}
