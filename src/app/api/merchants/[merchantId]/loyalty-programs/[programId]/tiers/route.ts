import { NextRequest, NextResponse } from 'next/server';
import { loyaltyService } from '@/services/loyaltyService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for creating a loyalty tier
const createLoyaltyTierSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  requiredPoints: z.number().int().min(0, 'Required points must be a non-negative integer'),
  benefits: z.array(z.string()).default([]),
  multiplier: z.number().min(1, 'Multiplier must be at least 1').default(1),
});

/**
 * GET handler for fetching loyalty tiers
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; programId: string } }
) {
  try {
    // Get the merchant ID and program ID from the URL
    const { merchantId, programId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get loyalty tiers
    const tiers = await loyaltyService.getLoyaltyTiers(merchantId, programId);
    
    return NextResponse.json(tiers);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/loyalty-programs/[programId]/tiers:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a loyalty tier
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; programId: string } }
) {
  try {
    // Get the merchant ID and program ID from the URL
    const { merchantId, programId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createLoyaltyTierSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the loyalty tier
    const tier = await loyaltyService.createLoyaltyTier(
      merchantId,
      programId,
      validationResult.data
    );
    
    return NextResponse.json(tier, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/loyalty-programs/[programId]/tiers:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
