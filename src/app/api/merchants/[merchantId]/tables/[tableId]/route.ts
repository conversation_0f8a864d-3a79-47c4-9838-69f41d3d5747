import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { updateTableSchema } from '@/lib/validations/reservationSchema';
import { checkAuth } from '@/lib/auth';

/**
 * GET handler for fetching a specific table
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; tableId: string } }
) {
  try {
    // Get the merchant ID and table ID from the URL
    const { merchantId, tableId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get all tables
    const tables = await reservationService.getTables(merchantId);
    
    // Find the specific table
    const table = tables.find(t => t.id === tableId);
    
    if (!table) {
      return NextResponse.json(
        { error: 'Table not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(table);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/tables/[tableId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a table
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; tableId: string } }
) {
  try {
    // Get the merchant ID and table ID from the URL
    const { merchantId, tableId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateTableSchema.safeParse({
      ...body,
      id: tableId,
      merchantId,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the table
    const table = await reservationService.updateTable(merchantId, tableId, body);
    
    return NextResponse.json(table);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/tables/[tableId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a table
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; tableId: string } }
) {
  try {
    // Get the merchant ID and table ID from the URL
    const { merchantId, tableId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the table
    await reservationService.deleteTable(merchantId, tableId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/tables/[tableId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
