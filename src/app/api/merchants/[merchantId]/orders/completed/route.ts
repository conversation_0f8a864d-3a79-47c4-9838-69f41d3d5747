import { NextRequest, NextResponse } from 'next/server';
import { checkAuth } from '@/lib/auth';

// Mock orders data (imported from the main orders route)
import { orders } from '../route';

/**
 * GET handler for fetching completed orders
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const merchantId = params.merchantId;

    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get orders for the merchant
    const merchantOrders = orders[merchantId] || [];

    // Filter completed orders
    const completedOrders = merchantOrders.filter(order =>
      order.status === 'completed' || order.status === 'delivered'
    );

    return NextResponse.json(completedOrders);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/orders/completed:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
