import { NextRequest, NextResponse } from 'next/server';
import { paymentService } from '@/services/paymentService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for processing an appointment payment
const processPaymentSchema = z.object({
  paymentMethod: z.enum(['credit_card', 'debit_card', 'bank_transfer', 'cash', 'other']),
  paymentMethodDetails: z.record(z.any()).optional(),
});

/**
 * POST handler for processing an appointment payment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; appointmentId: string } }
) {
  try {
    // Get the merchant ID and appointment ID from the URL
    const { merchantId, appointmentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = processPaymentSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Process the payment
    const { paymentMethod, paymentMethodDetails } = validationResult.data;
    
    const payment = await paymentService.processAppointmentPayment(
      merchantId,
      appointmentId,
      paymentMethod,
      paymentMethodDetails
    );
    
    return NextResponse.json(payment, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/appointments/[appointmentId]/process-payment:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
