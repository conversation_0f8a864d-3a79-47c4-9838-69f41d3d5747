/**
 * API route for tables
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);

    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/tables${queryString ? `?${queryString}` : ''}`;

    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/branches/[branchId]/tables:', error);

    // Return mock data for development
    const mockData = [
      {
        id: '1',
        name: 'Table 1',
        number: 1,
        capacity: 4,
        area: 'Dining Area',
        status: 'available',
        position: { x: 100, y: 100 },
        shape: 'square',
        size: { width: 80, height: 80 },
        qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=table-1',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Table 2',
        number: 2,
        capacity: 2,
        area: 'Dining Area',
        status: 'occupied',
        position: { x: 200, y: 100 },
        shape: 'round',
        size: { width: 60, height: 60 },
        qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=table-2',
        currentOrder: {
          id: 'order-1',
          orderNumber: 'ORD-001',
          total: 45.99,
          status: 'preparing'
        },
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Table 3',
        number: 3,
        capacity: 6,
        area: 'Dining Area',
        status: 'reserved',
        position: { x: 300, y: 100 },
        shape: 'rectangle',
        size: { width: 120, height: 80 },
        qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=table-3',
        currentReservation: {
          id: 'reservation-1',
          customerName: 'John Doe',
          partySize: 6,
          time: '19:00'
        },
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '4',
        name: 'Table 4',
        number: 4,
        capacity: 4,
        area: 'Outdoor Patio',
        status: 'available',
        position: { x: 100, y: 250 },
        shape: 'square',
        size: { width: 80, height: 80 },
        qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=table-4',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '5',
        name: 'Table 5',
        number: 5,
        capacity: 2,
        area: 'Outdoor Patio',
        status: 'cleaning',
        position: { x: 200, y: 250 },
        shape: 'round',
        size: { width: 60, height: 60 },
        qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=table-5',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '6',
        name: 'Table 6',
        number: 6,
        capacity: 8,
        area: 'Outdoor Patio',
        status: 'maintenance',
        position: { x: 300, y: 250 },
        shape: 'rectangle',
        size: { width: 140, height: 80 },
        qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=table-6',
        isActive: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    return NextResponse.json(mockData);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const body = await request.json();

    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/tables`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating table:', error);

    // Return mock created table for development
    const mockTable = {
      id: `table-${Date.now()}`,
      name: `Table ${Date.now()}`,
      number: Math.floor(Math.random() * 100) + 1,
      capacity: 4,
      area: 'Dining Area',
      status: 'available',
      position: { x: 100, y: 100 },
      shape: 'square',
      size: { width: 80, height: 80 },
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=table-${Date.now()}`,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json(mockTable);
  }
}
