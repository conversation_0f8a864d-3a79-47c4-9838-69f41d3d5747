import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Mock customer data generator
const generateMockCustomerData = (dateRange: string) => {
  const now = new Date();
  const days = dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : 90;
  
  // Generate customer growth data
  const customerGrowthData = [];
  let cumulativeCustomers = 2000;
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    const newCustomers = Math.floor(5 + Math.random() * 15);
    cumulativeCustomers += newCustomers;
    
    customerGrowthData.push({
      date: date.toISOString().split('T')[0],
      newCustomers,
      totalCustomers: cumulativeCustomers,
      returningCustomers: Math.floor(newCustomers * 0.3),
    });
  }
  
  // Generate top customers
  const customerNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  ];
  
  const topCustomers = customerNames.map((name, index) => ({
    name,
    email: `${name.toLowerCase().replace(' ', '.')}@email.com`,
    orders: Math.floor(50 - index * 5 + Math.random() * 10),
    totalSpent: `$${Math.floor(2000 - index * 200 + Math.random() * 400)}`,
    lastVisit: `${Math.floor(Math.random() * 7) + 1} days ago`,
    status: index < 2 ? 'vip' : index < 6 ? 'regular' : 'new',
  }));
  
  return {
    customerGrowthData,
    topCustomers,
  };
};

export async function GET(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '30d';
    const branchId = searchParams.get('branchId');

    // Generate mock data
    const { customerGrowthData, topCustomers } = generateMockCustomerData(dateRange);
    
    // Calculate metrics
    const totalCustomers = customerGrowthData[customerGrowthData.length - 1]?.totalCustomers || 0;
    const newCustomersCount = customerGrowthData.reduce((sum, day) => sum + day.newCustomers, 0);
    const returningCustomersCount = customerGrowthData.reduce((sum, day) => sum + day.returningCustomers, 0);
    const retentionRate = totalCustomers > 0 ? (returningCustomersCount / totalCustomers * 100) : 0;
    
    const metrics = {
      totalCustomers: {
        value: totalCustomers.toLocaleString(),
        change: '+15.3%',
        trend: 'up',
      },
      newCustomers: {
        value: newCustomersCount.toString(),
        change: '+22.1%',
        trend: 'up',
      },
      returningCustomers: {
        value: returningCustomersCount.toString(),
        change: '+8.7%',
        trend: 'up',
      },
      retentionRate: {
        value: `${retentionRate.toFixed(1)}%`,
        change: '+3.2%',
        trend: 'up',
      },
    };

    // Customer segments
    const customerSegments = [
      {
        name: 'VIP Customers',
        count: Math.floor(totalCustomers * 0.05),
        percentage: 5,
        avgSpend: '$125.50',
        color: 'purple',
      },
      {
        name: 'Regular Customers',
        count: Math.floor(totalCustomers * 0.51),
        percentage: 51,
        avgSpend: '$45.20',
        color: 'blue',
      },
      {
        name: 'Occasional Customers',
        count: Math.floor(totalCustomers * 0.35),
        percentage: 35,
        avgSpend: '$28.75',
        color: 'green',
      },
      {
        name: 'New Customers',
        count: Math.floor(totalCustomers * 0.09),
        percentage: 9,
        avgSpend: '$32.10',
        color: 'yellow',
      },
    ];

    // Customer acquisition sources
    const acquisitionSources = [
      { source: 'Direct', customers: 456, percentage: 32 },
      { source: 'Social Media', customers: 342, percentage: 24 },
      { source: 'Referrals', customers: 298, percentage: 21 },
      { source: 'Search', customers: 187, percentage: 13 },
      { source: 'Email', customers: 143, percentage: 10 },
    ];

    // Demographics
    const demographics = {
      ageGroups: [
        { range: '18-25', percentage: 18 },
        { range: '26-35', percentage: 35 },
        { range: '36-50', percentage: 32 },
        { range: '50+', percentage: 15 },
      ],
      visitFrequency: [
        { frequency: 'Daily', percentage: 8 },
        { frequency: 'Weekly', percentage: 25 },
        { frequency: 'Monthly', percentage: 42 },
        { frequency: 'Occasionally', percentage: 25 },
      ],
    };

    // Customer satisfaction
    const satisfaction = {
      averageRating: 4.6,
      totalReviews: 1247,
      ratingDistribution: [
        { stars: 5, percentage: 68 },
        { stars: 4, percentage: 22 },
        { stars: 3, percentage: 7 },
        { stars: 2, percentage: 2 },
        { stars: 1, percentage: 1 },
      ],
    };

    // Customer lifetime value
    const lifetimeValue = {
      average: '$342.50',
      segments: {
        vip: '$1,250.00',
        regular: '$485.75',
        occasional: '$156.25',
        new: '$89.50',
      },
    };

    return NextResponse.json({
      metrics,
      customerGrowthData,
      topCustomers,
      customerSegments,
      acquisitionSources,
      demographics,
      satisfaction,
      lifetimeValue,
      dateRange,
      generatedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Customer data API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
