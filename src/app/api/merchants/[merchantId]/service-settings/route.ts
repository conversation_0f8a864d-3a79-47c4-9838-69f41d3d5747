import { NextRequest, NextResponse } from 'next/server';
import { settingsService } from '@/services/settingsService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for service settings
const serviceSettingsSchema = z.object({
  appointmentEnabled: z.boolean().optional(),
  serviceLocations: z.array(z.string()).optional(),
  cancellationPolicy: z.string().optional(),
  openingHours: z.record(z.object({
    open: z.string(),
    close: z.string(),
  })).optional(),
  bufferTimeBetweenAppointments: z.number().int().min(0).optional(),
  maxAdvanceBookingDays: z.number().int().min(1).optional(),
  minAdvanceBookingHours: z.number().int().min(0).optional(),
  allowCancellationBefore: z.number().int().min(0).optional(),
  sendReminders: z.boolean().optional(),
  reminderHours: z.array(z.number().int().min(0)).optional(),
});

/**
 * GET handler for fetching service settings
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get settings for the merchant
    const settings = await settingsService.getSettings(merchantId);
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/service-settings:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating service settings
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = serviceSettingsSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the settings
    const settings = await settingsService.updateSettings(merchantId, validationResult.data);
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/service-settings:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
