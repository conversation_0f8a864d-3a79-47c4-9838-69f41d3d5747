import { NextRequest, NextResponse } from 'next/server';
import { staffService } from '@/services/staffService';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * GET handler for checking staff availability
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; staffId: string } }
) {
  try {
    // Get the merchant ID and staff ID from the URL
    const { merchantId, staffId } = params;
    
    // Get the date from the query parameters
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    
    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      );
    }
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get availability for the staff member on the specified date
    const availability = await staffService.getStaffAvailability(merchantId, staffId, date);
    
    return NextResponse.json(availability);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/staff/[staffId]/availability:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
