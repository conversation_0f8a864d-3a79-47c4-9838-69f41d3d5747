/**
 * API route for sales trends
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/reports/sales-trends${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching sales trends:', error);
    
    // Return mock data for development
    const period = searchParams.get('period') || 'month';
    const mockData = generateMockSalesTrends(period);
    
    return NextResponse.json(mockData);
  }
}

function generateMockSalesTrends(period: string) {
  const now = new Date();
  const data = [];
  
  switch (period) {
    case 'day':
      // Last 7 days
      for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        data.push({
          period: date.toISOString().split('T')[0],
          sales: Math.floor(Math.random() * 3000) + 1000,
          orders: Math.floor(Math.random() * 50) + 20,
          date: date.toISOString(),
          name: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        });
      }
      break;
      
    case 'week':
      // Last 8 weeks
      for (let i = 7; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - (i * 7));
        data.push({
          period: date.toISOString().split('T')[0],
          sales: Math.floor(Math.random() * 15000) + 8000,
          orders: Math.floor(Math.random() * 200) + 100,
          date: date.toISOString(),
          name: `Week ${8 - i}`
        });
      }
      break;
      
    case 'month':
      // Last 6 months
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now);
        date.setMonth(date.getMonth() - i);
        data.push({
          period: date.toISOString().split('T')[0],
          sales: Math.floor(Math.random() * 50000) + 30000,
          orders: Math.floor(Math.random() * 800) + 400,
          date: date.toISOString(),
          name: date.toLocaleDateString('en-US', { month: 'short' })
        });
      }
      break;
      
    case 'quarter':
      // Last 4 quarters
      for (let i = 3; i >= 0; i--) {
        const date = new Date(now);
        date.setMonth(date.getMonth() - (i * 3));
        data.push({
          period: date.toISOString().split('T')[0],
          sales: Math.floor(Math.random() * 150000) + 100000,
          orders: Math.floor(Math.random() * 2000) + 1000,
          date: date.toISOString(),
          name: `Q${Math.ceil((date.getMonth() + 1) / 3)}`
        });
      }
      break;
      
    case 'year':
      // Last 3 years
      for (let i = 2; i >= 0; i--) {
        const date = new Date(now);
        date.setFullYear(date.getFullYear() - i);
        data.push({
          period: date.toISOString().split('T')[0],
          sales: Math.floor(Math.random() * 500000) + 400000,
          orders: Math.floor(Math.random() * 8000) + 4000,
          date: date.toISOString(),
          name: date.getFullYear().toString()
        });
      }
      break;
      
    default:
      // Default to monthly data
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now);
        date.setMonth(date.getMonth() - i);
        data.push({
          period: date.toISOString().split('T')[0],
          sales: Math.floor(Math.random() * 50000) + 30000,
          orders: Math.floor(Math.random() * 800) + 400,
          date: date.toISOString(),
          name: date.toLocaleDateString('en-US', { month: 'short' })
        });
      }
  }
  
  return data;
}
