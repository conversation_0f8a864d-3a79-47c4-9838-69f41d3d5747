import { NextRequest, NextResponse } from 'next/server';
import { calendarService } from '@/services/calendarService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating calendar integration
const updateCalendarIntegrationSchema = z.object({
  provider: z.enum(['google', 'outlook', 'apple', 'custom']).optional(),
  name: z.string().min(1, 'Name is required').optional(),
  calendarId: z.string().min(1, 'Calendar ID is required').optional(),
  accessToken: z.string().min(1, 'Access token is required').optional(),
  refreshToken: z.string().optional(),
  tokenExpiry: z.string().optional().transform(val => val ? new Date(val) : undefined),
  settings: z.object({
    syncEnabled: z.boolean().optional(),
    syncDirection: z.enum(['import', 'export', 'both']).optional(),
    colorId: z.string().optional(),
    defaultReminders: z.array(
      z.object({
        method: z.string(),
        minutes: z.number().int(),
      })
    ).optional(),
  }).optional(),
  userId: z.string().optional(),
});

/**
 * GET handler for fetching a specific calendar integration
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; integrationId: string } }
) {
  try {
    // Get the merchant ID and integration ID from the URL
    const { merchantId, integrationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the calendar integration
    const integration = await calendarService.getCalendarIntegrationById(merchantId, integrationId);
    
    return NextResponse.json(integration);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/calendar-integrations/[integrationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a calendar integration
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; integrationId: string } }
) {
  try {
    // Get the merchant ID and integration ID from the URL
    const { merchantId, integrationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateCalendarIntegrationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the calendar integration
    const integration = await calendarService.updateCalendarIntegration(
      merchantId,
      integrationId,
      validationResult.data
    );
    
    return NextResponse.json(integration);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/calendar-integrations/[integrationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a calendar integration
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; integrationId: string } }
) {
  try {
    // Get the merchant ID and integration ID from the URL
    const { merchantId, integrationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the calendar integration
    await calendarService.deleteCalendarIntegration(merchantId, integrationId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/calendar-integrations/[integrationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
