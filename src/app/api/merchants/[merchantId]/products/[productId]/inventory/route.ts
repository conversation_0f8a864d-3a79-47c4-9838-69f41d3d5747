import { NextRequest, NextResponse } from 'next/server';
import { inventoryService } from '@/services/inventoryService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for creating an inventory transaction
const createInventoryTransactionSchema = z.object({
  type: z.enum(['purchase', 'sale', 'adjustment', 'return', 'transfer']),
  quantity: z.number().int().noneOf([0], 'Quantity cannot be zero'),
  reference: z.string().optional(),
  notes: z.string().optional(),
});

/**
 * GET handler for fetching inventory transactions
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; productId: string } }
) {
  try {
    // Get the merchant ID and product ID from the URL
    const { merchantId, productId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get inventory transactions
    const transactions = await inventoryService.getInventoryTransactions(merchantId, productId);
    
    return NextResponse.json(transactions);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/products/[productId]/inventory:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating an inventory transaction
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; productId: string } }
) {
  try {
    // Get the merchant ID and product ID from the URL
    const { merchantId, productId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createInventoryTransactionSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the inventory transaction
    const result = await inventoryService.createInventoryTransaction({
      ...validationResult.data,
      merchantId,
      productId,
    });
    
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/products/[productId]/inventory:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
