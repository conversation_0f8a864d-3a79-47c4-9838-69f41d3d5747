import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Base query using Next.js API routes (proxy to backend)
const baseQuery = fetchBaseQuery({
  baseUrl: '/api',
  prepareHeaders: async (headers) => {
    // Set content type
    headers.set('content-type', 'application/json');

    // NextAuth session will be handled server-side in API routes
    // No need to manually add auth headers here

    return headers;
  },
});

// Create the API slice
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery,
  tagTypes: ['Merchants', 'Items', 'Orders', 'Users', 'Settings', 'Communications', 'Appointments', 'Campaigns', 'CampaignSegments', 'CommunicationAnalytics', 'Reviews', 'Reservations', 'Tables', 'TableAreas', 'TableLayout', 'Shops', 'Shop', 'Branches', 'Floor', 'PurchaseOrders', 'Suppliers'],
  endpoints: () => ({}),
});
