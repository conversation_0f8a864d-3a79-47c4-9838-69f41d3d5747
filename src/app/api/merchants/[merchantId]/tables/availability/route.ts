import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';
import { z } from 'zod';

// Schema for availability request
const availabilityRequestSchema = z.object({
  date: z.string().min(1, 'Date is required'),
  partySize: z.number().int().min(1, 'Party size must be at least 1'),
});

/**
 * GET handler for checking table availability
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const partySizeParam = searchParams.get('partySize');
    
    if (!date || !partySizeParam) {
      return NextResponse.json(
        { error: 'Date and party size are required' },
        { status: 400 }
      );
    }
    
    const partySize = parseInt(partySizeParam, 10);
    
    // Validate parameters
    const validationResult = availabilityRequestSchema.safeParse({
      date,
      partySize,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Get available time slots
    const timeSlots = await reservationService.getAvailableTimeSlots(
      merchantId,
      date,
      partySize
    );
    
    return NextResponse.json(timeSlots);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/tables/availability:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
