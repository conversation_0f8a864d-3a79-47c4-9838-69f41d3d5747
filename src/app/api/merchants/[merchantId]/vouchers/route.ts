import { NextRequest, NextResponse } from 'next/server';
import { voucherService } from '@/services/voucherService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for creating a voucher
const createVoucherSchema = z.object({
  code: z.string().min(3, 'Code must be at least 3 characters'),
  type: z.enum(['percentage', 'fixed_amount', 'free_service']),
  value: z.number().positive('Value must be positive'),
  minSpend: z.number().optional(),
  maxDiscount: z.number().optional(),
  serviceId: z.string().optional(),
  categoryId: z.string().optional(),
  startDate: z.string().transform(val => new Date(val)),
  endDate: z.string().transform(val => new Date(val)),
  usageLimit: z.number().int().optional(),
  isActive: z.boolean().default(true),
  isGiftCard: z.boolean().default(false),
  recipientEmail: z.string().email().optional(),
  recipientName: z.string().optional(),
  senderName: z.string().optional(),
  message: z.string().optional(),
});

/**
 * GET handler for fetching vouchers
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('includeInactive') === 'true';
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get vouchers
    const vouchers = await voucherService.getVouchers(merchantId, includeInactive);
    
    return NextResponse.json(vouchers);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/vouchers:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a voucher
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createVoucherSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the voucher
    const voucher = await voucherService.createVoucher({
      ...validationResult.data,
      merchantId,
    });
    
    return NextResponse.json(voucher, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/vouchers:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
