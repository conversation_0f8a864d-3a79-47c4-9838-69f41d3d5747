import { NextRequest, NextResponse } from 'next/server';
import { appointmentCommunicationService } from '@/services';
import { supabase } from '@/lib/supabase/client';

/**
 * POST /api/merchants/[merchantId]/appointments/[appointmentId]/communications/schedule-reminders
 * Schedule reminders for an appointment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; appointmentId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Schedule reminders
    const scheduledCommunications = await appointmentCommunicationService.scheduleAppointmentReminders(
      params.merchantId,
      params.appointmentId
    );

    return NextResponse.json(scheduledCommunications, { status: 200 });
  } catch (error) {
    console.error('Error scheduling appointment reminders:', error);
    return NextResponse.json(
      { error: 'Failed to schedule appointment reminders' },
      { status: 500 }
    );
  }
}
