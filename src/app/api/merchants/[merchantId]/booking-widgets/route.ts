import { NextRequest, NextResponse } from 'next/server';
import { bookingWidgetService } from '@/services/bookingWidgetService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for creating a booking widget
const createBookingWidgetSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  allowedOrigins: z.array(z.string()).default([]),
  settings: z.object({
    theme: z.object({
      primaryColor: z.string().default('#4f46e5'),
      secondaryColor: z.string().default('#f59e0b'),
      fontFamily: z.string().default('Inter, sans-serif'),
      borderRadius: z.string().default('0.375rem'),
      buttonStyle: z.string().default('filled'),
    }).default({}),
    layout: z.object({
      showHeader: z.boolean().default(true),
      showFooter: z.boolean().default(true),
      showLogo: z.boolean().default(true),
      logoUrl: z.string().optional(),
      customCss: z.string().optional(),
    }).default({}),
    booking: z.object({
      requireCustomerDetails: z.boolean().default(true),
      requireCustomerPhone: z.boolean().default(true),
      requireCustomerEmail: z.boolean().default(true),
      allowGuestBooking: z.boolean().default(false),
      showPrices: z.boolean().default(true),
      showDuration: z.boolean().default(true),
      showAvailableStaff: z.boolean().default(true),
      enableServiceFiltering: z.boolean().default(true),
      enableStaffSelection: z.boolean().default(true),
      maxDaysInAdvance: z.number().int().min(1).default(30),
      minHoursInAdvance: z.number().int().min(0).default(1),
      timeSlotInterval: z.number().int().min(5).default(15),
    }).default({}),
    services: z.object({
      includedServiceIds: z.array(z.string()).default([]),
      excludedServiceIds: z.array(z.string()).default([]),
      includedCategoryIds: z.array(z.string()).default([]),
      excludedCategoryIds: z.array(z.string()).default([]),
    }).default({}),
    staff: z.object({
      includedStaffIds: z.array(z.string()).default([]),
      excludedStaffIds: z.array(z.string()).default([]),
    }).default({}),
  }).default({}),
  isActive: z.boolean().default(true),
});

/**
 * GET handler for fetching booking widgets
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get booking widgets
    const widgets = await bookingWidgetService.getBookingWidgets(merchantId);
    
    return NextResponse.json(widgets);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/booking-widgets:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a booking widget
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createBookingWidgetSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the booking widget
    const widget = await bookingWidgetService.createBookingWidget({
      ...validationResult.data,
      merchantId,
    });
    
    return NextResponse.json(widget, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/booking-widgets:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
