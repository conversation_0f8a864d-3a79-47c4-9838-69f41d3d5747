/**
 * API route for individual shop branch management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    
    const response = await serverFetchClient(`/shops/${shopId}/branches/${branchId}`, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/shops/[shopId]/branches/[branchId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch shop branch' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    const body = await request.json();
    
    const response = await serverFetchClient(`/shops/${shopId}/branches/${branchId}`, request, {
      method: 'PUT',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/shops/[shopId]/branches/[branchId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update shop branch' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    
    const response = await serverFetchClient(`/shops/${shopId}/branches/${branchId}`, request, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in DELETE /api/shops/[shopId]/branches/[branchId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete shop branch' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
