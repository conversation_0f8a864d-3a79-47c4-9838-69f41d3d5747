import { NextRequest, NextResponse } from 'next/server';
import { loyaltyService } from '@/services/loyaltyService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for creating a loyalty program
const createLoyaltyProgramSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  pointsPerPurchase: z.number().int().min(0, 'Points per purchase must be a non-negative integer'),
  pointsPerAmount: z.number().int().min(0, 'Points per amount must be a non-negative integer'),
  amountPerPoint: z.number().positive('Amount per point must be positive'),
  minimumPointsRedemption: z.number().int().min(0, 'Minimum points redemption must be a non-negative integer'),
  pointsValueInCurrency: z.number().positive('Points value in currency must be positive'),
  expirationMonths: z.number().int().min(0, 'Expiration months must be a non-negative integer').optional(),
  isActive: z.boolean().default(true),
});

/**
 * GET handler for fetching loyalty programs
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get loyalty programs
    const programs = await loyaltyService.getLoyaltyPrograms(merchantId);
    
    return NextResponse.json(programs);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/loyalty-programs:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a loyalty program
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createLoyaltyProgramSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the loyalty program
    const program = await loyaltyService.createLoyaltyProgram({
      ...validationResult.data,
      merchantId,
    });
    
    return NextResponse.json(program, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/loyalty-programs:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
