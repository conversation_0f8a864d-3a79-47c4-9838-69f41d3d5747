import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';



/**
 * GET /api/merchants/[merchantId]/campaign-segments
 * Get all campaign segments for a merchant
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string }> }
) {
  try {
    const { merchantId } = await params;
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/campaign-segments${queryString ? `?${queryString}` : ''}`;

    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching campaign segments:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch campaign segments' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

/**
 * POST /api/merchants/[merchantId]/campaign-segments
 * Create a new campaign segment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string }> }
) {
  try {
    const { merchantId } = await params;
    const body = await request.json();

    const response = await fetchClient(`/merchants/${merchantId}/campaign-segments`, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error creating campaign segment:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create campaign segment' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
