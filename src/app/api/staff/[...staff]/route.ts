/**
 * API catch-all route for staff management
 * Handles all staff-related operations and proxies to backend using slugs
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ staff: string[] }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const shopSlug = searchParams.get('shopSlug');
    const branchSlug = searchParams.get('branchSlug');
    const resolvedParams = await params;
    const staffPath = resolvedParams.staff.join('/');

    if (!shopSlug) {
      return NextResponse.json(
        { error: 'shopSlug is required' },
        { status: 400 }
      );
    }

    if (!branchSlug) {
      return NextResponse.json(
        { error: 'branchSlug is required' },
        { status: 400 }
      );
    }

    // Build backend URL using slug-based routing
    let url = `/shops/slug/${shopSlug}/branches/${branchSlug}/staff`;

    // Add the staff path if provided
    if (staffPath) {
      url += `/${staffPath}`;
    }

    // Add query parameters
    const queryParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      if (key !== 'shopSlug' && key !== 'branchSlug') {
        queryParams.append(key, value);
      }
    });

    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }

    console.log('Fetching staff from backend (slug-based):', {
      url,
      shopSlug,
      branchSlug,
      staffPath,
      queryParams: Object.fromEntries(queryParams)
    });

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/staff/[...staff]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch staff data' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ staff: string[] }> }
) {
  try {
    const body = await request.json();
    const { shopSlug, branchSlug, ...staffData } = body;
    const resolvedParams = await params;
    const staffPath = resolvedParams.staff.join('/');

    if (!shopSlug) {
      return NextResponse.json(
        { error: 'shopSlug is required' },
        { status: 400 }
      );
    }

    if (!branchSlug) {
      return NextResponse.json(
        { error: 'branchSlug is required' },
        { status: 400 }
      );
    }

    // Build backend URL using slug-based routing
    let url = `/shops/slug/${shopSlug}/branches/${branchSlug}/staff`;

    // Add the staff path if provided
    if (staffPath) {
      url += `/${staffPath}`;
    }

    console.log('Creating/updating staff:', {
      url,
      shopSlug,
      branchSlug,
      staffPath,
      data: staffData
    });

    const response = await serverFetchClient(url, request, {
      method: 'POST',
      body: JSON.stringify(staffData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/staff/[...staff]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create staff data' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ staff: string[] }> }
) {
  try {
    const body = await request.json();
    const { shopSlug, branchSlug, ...staffData } = body;
    const resolvedParams = await params;
    const staffPath = resolvedParams.staff.join('/');

    if (!shopSlug) {
      return NextResponse.json(
        { error: 'shopSlug is required' },
        { status: 400 }
      );
    }

    if (!branchSlug) {
      return NextResponse.json(
        { error: 'branchSlug is required' },
        { status: 400 }
      );
    }

    // Build backend URL using slug-based routing
    let url = `/shops/slug/${shopSlug}/branches/${branchSlug}/staff`;

    // Add the staff path if provided
    if (staffPath) {
      url += `/${staffPath}`;
    }

    console.log('Updating staff:', {
      url,
      shopSlug,
      branchSlug,
      staffPath,
      data: staffData
    });

    const response = await serverFetchClient(url, request, {
      method: 'PUT',
      body: JSON.stringify(staffData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/staff/[...staff]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update staff data' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ staff: string[] }> }
) {
  try {
    const body = await request.json();
    const { shopSlug, branchSlug, ...staffData } = body;
    const resolvedParams = await params;
    const staffPath = resolvedParams.staff.join('/');

    if (!shopSlug) {
      return NextResponse.json(
        { error: 'shopSlug is required' },
        { status: 400 }
      );
    }

    if (!branchSlug) {
      return NextResponse.json(
        { error: 'branchSlug is required' },
        { status: 400 }
      );
    }

    // Build backend URL using slug-based routing
    let url = `/shops/slug/${shopSlug}/branches/${branchSlug}/staff`;

    // Add the staff path if provided
    if (staffPath) {
      url += `/${staffPath}`;
    }

    console.log('Patching staff:', {
      url,
      shopSlug,
      branchSlug,
      staffPath,
      data: staffData
    });

    const response = await serverFetchClient(url, request, {
      method: 'PATCH',
      body: JSON.stringify(staffData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PATCH /api/staff/[...staff]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to patch staff data' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ staff: string[] }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const shopSlug = searchParams.get('shopSlug');
    const branchSlug = searchParams.get('branchSlug');
    const resolvedParams = await params;
    const staffPath = resolvedParams.staff.join('/');

    if (!shopSlug) {
      return NextResponse.json(
        { error: 'shopSlug is required' },
        { status: 400 }
      );
    }

    if (!branchSlug) {
      return NextResponse.json(
        { error: 'branchSlug is required' },
        { status: 400 }
      );
    }

    // Build backend URL using slug-based routing
    let url = `/shops/slug/${shopSlug}/branches/${branchSlug}/staff`;

    // Add the staff path if provided
    if (staffPath) {
      url += `/${staffPath}`;
    }

    // Add query parameters
    const queryParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      if (key !== 'shopSlug' && key !== 'branchSlug') {
        queryParams.append(key, value);
      }
    });

    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }

    console.log('Deleting staff:', {
      url,
      shopSlug,
      branchSlug,
      staffPath
    });

    const response = await serverFetchClient(url, request, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await handleApiResponse(response);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/staff/[...staff]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete staff data' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
