/**
 * API route for customer analytics
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/reports/customer-analytics${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching customer analytics:', error);
    
    // Return mock data for development
    const mockData = {
      totalCustomers: 1200,
      newCustomers: 180,
      returningCustomers: 1020,
      averageSpend: 21.21,
      repeatCustomerRate: 45.5,
      customerLifetimeValue: 156.78
    };
    
    return NextResponse.json(mockData);
  }
}
