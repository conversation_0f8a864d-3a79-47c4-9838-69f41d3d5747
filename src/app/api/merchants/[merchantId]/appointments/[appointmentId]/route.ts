import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

/**
 * GET handler for fetching a specific appointment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; appointmentId: string }> }
) {
  try {
    const { merchantId, appointmentId } = await params;

    const response = await fetchClient(`/merchants/${merchantId}/appointments/${appointmentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/appointments/[appointmentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch appointment' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

/**
 * PUT handler for updating a specific appointment
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; appointmentId: string }> }
) {
  try {
    const { merchantId, appointmentId } = await params;
    const body = await request.json();

    const response = await fetchClient(`/merchants/${merchantId}/appointments/${appointmentId}`, {
      method: 'PUT',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/appointments/[appointmentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update appointment' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

/**
 * DELETE handler for cancelling a specific appointment
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; appointmentId: string }> }
) {
  try {
    const { merchantId, appointmentId } = await params;

    const response = await fetchClient(`/merchants/${merchantId}/appointments/${appointmentId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/appointments/[appointmentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete appointment' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
