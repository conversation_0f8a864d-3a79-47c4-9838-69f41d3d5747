import { NextRequest, NextResponse } from 'next/server';
import { staffService } from '@/services/staffService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * POST handler for assigning services to a staff member
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; staffId: string } }
) {
  try {
    // Get the merchant ID and staff ID from the URL
    const { merchantId, staffId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const schema = z.object({
      serviceIds: z.array(z.string()),
    });
    
    const validationResult = schema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Assign services to the staff member
    const { serviceIds } = validationResult.data;
    const staff = await staffService.assignServices(merchantId, staffId, serviceIds);
    
    return NextResponse.json(staff);
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/staff/[staffId]/services:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
