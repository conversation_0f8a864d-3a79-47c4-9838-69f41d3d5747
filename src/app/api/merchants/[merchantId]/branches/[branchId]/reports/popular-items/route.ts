/**
 * API route for popular menu items
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/reports/popular-items${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching popular items:', error);
    
    // Return mock data for development
    const mockData = [
      {
        id: '1',
        name: 'Spicy Chicken Sandwich',
        category: 'Main Course',
        orders: 350,
        revenue: 3500,
        image: '/images/menu/chicken-sandwich.jpg',
        price: 10.00
      },
      {
        id: '2',
        name: 'Avocado Toast',
        category: 'Appetizer',
        orders: 280,
        revenue: 2240,
        image: '/images/menu/avocado-toast.jpg',
        price: 8.00
      },
      {
        id: '3',
        name: 'Classic Burger',
        category: 'Main Course',
        orders: 250,
        revenue: 2500,
        image: '/images/menu/classic-burger.jpg',
        price: 10.00
      },
      {
        id: '4',
        name: 'Caesar Salad',
        category: 'Appetizer',
        orders: 220,
        revenue: 1760,
        image: '/images/menu/caesar-salad.jpg',
        price: 8.00
      },
      {
        id: '5',
        name: 'Chocolate Brownie',
        category: 'Dessert',
        orders: 200,
        revenue: 1200,
        image: '/images/menu/chocolate-brownie.jpg',
        price: 6.00
      },
      {
        id: '6',
        name: 'Margherita Pizza',
        category: 'Main Course',
        orders: 195,
        revenue: 2340,
        image: '/images/menu/margherita-pizza.jpg',
        price: 12.00
      },
      {
        id: '7',
        name: 'Fish & Chips',
        category: 'Main Course',
        orders: 180,
        revenue: 2520,
        image: '/images/menu/fish-chips.jpg',
        price: 14.00
      },
      {
        id: '8',
        name: 'Greek Salad',
        category: 'Appetizer',
        orders: 165,
        revenue: 1320,
        image: '/images/menu/greek-salad.jpg',
        price: 8.00
      },
      {
        id: '9',
        name: 'Tiramisu',
        category: 'Dessert',
        orders: 150,
        revenue: 1050,
        image: '/images/menu/tiramisu.jpg',
        price: 7.00
      },
      {
        id: '10',
        name: 'Chicken Wings',
        category: 'Appetizer',
        orders: 140,
        revenue: 1260,
        image: '/images/menu/chicken-wings.jpg',
        price: 9.00
      }
    ];
    
    return NextResponse.json(mockData);
  }
}
