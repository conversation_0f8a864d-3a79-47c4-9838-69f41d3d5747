import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';

/**
 * GET handler for fetching today's reservations
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get today's date
    const today = new Date().toISOString().split('T')[0];
    
    // Get today's reservations
    const reservations = await reservationService.getReservations(merchantId);
    const todayReservations = reservations.filter(reservation => 
      reservation.date === today
    );
    
    return NextResponse.json(todayReservations);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/reservations/today:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
