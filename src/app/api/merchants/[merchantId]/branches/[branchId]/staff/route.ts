/**
 * API route for staff
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/staff${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching staff:', error);
    
    // Return mock data for development
    const mockData = {
      data: [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '+1234567890',
          slug: 'john-smith',
          avatar: '/images/staff/john-smith.jpg',
          position: 'Head Chef',
          department: 'Kitchen',
          roleId: 'role-1',
          roleName: 'Chef',
          permissions: ['manage_menu', 'view_orders', 'update_orders'],
          status: 'active',
          employeeId: 'EMP001',
          hireDate: '2023-01-15',
          salary: 65000,
          address: {
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            country: 'USA'
          },
          emergencyContact: {
            name: 'Jane Smith',
            relationship: 'Spouse',
            phone: '+1234567891'
          },
          schedule: [
            { dayOfWeek: 1, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 2, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 3, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 4, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 5, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 6, startTime: '00:00', endTime: '00:00', isWorkingDay: false },
            { dayOfWeek: 0, startTime: '00:00', endTime: '00:00', isWorkingDay: false }
          ],
          performance: {
            ordersServed: 1250,
            customerRating: 4.8,
            punctualityScore: 95,
            salesGenerated: 45000,
            lastReviewDate: '2024-01-01'
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          firstName: 'Sarah',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '+1234567892',
          slug: 'sarah-johnson',
          avatar: '/images/staff/sarah-johnson.jpg',
          position: 'Server',
          department: 'Front of House',
          roleId: 'role-2',
          roleName: 'Server',
          permissions: ['view_orders', 'take_orders', 'process_payments'],
          status: 'active',
          employeeId: 'EMP002',
          hireDate: '2023-03-20',
          hourlyRate: 18.50,
          address: {
            street: '456 Oak Ave',
            city: 'New York',
            state: 'NY',
            zipCode: '10002',
            country: 'USA'
          },
          emergencyContact: {
            name: 'Mike Johnson',
            relationship: 'Brother',
            phone: '+1234567893'
          },
          schedule: [
            { dayOfWeek: 1, startTime: '11:00', endTime: '19:00', isWorkingDay: true },
            { dayOfWeek: 2, startTime: '11:00', endTime: '19:00', isWorkingDay: true },
            { dayOfWeek: 3, startTime: '11:00', endTime: '19:00', isWorkingDay: true },
            { dayOfWeek: 4, startTime: '11:00', endTime: '19:00', isWorkingDay: true },
            { dayOfWeek: 5, startTime: '11:00', endTime: '21:00', isWorkingDay: true },
            { dayOfWeek: 6, startTime: '10:00', endTime: '22:00', isWorkingDay: true },
            { dayOfWeek: 0, startTime: '10:00', endTime: '20:00', isWorkingDay: true }
          ],
          performance: {
            ordersServed: 2100,
            customerRating: 4.9,
            punctualityScore: 98,
            salesGenerated: 78000,
            lastReviewDate: '2024-01-01'
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          firstName: 'Mike',
          lastName: 'Davis',
          email: '<EMAIL>',
          phone: '+1234567894',
          slug: 'mike-davis',
          avatar: '/images/staff/mike-davis.jpg',
          position: 'Manager',
          department: 'Management',
          roleId: 'role-3',
          roleName: 'Manager',
          permissions: ['manage_staff', 'manage_menu', 'view_reports', 'manage_reservations'],
          status: 'active',
          employeeId: 'EMP003',
          hireDate: '2022-08-10',
          salary: 75000,
          address: {
            street: '789 Pine St',
            city: 'New York',
            state: 'NY',
            zipCode: '10003',
            country: 'USA'
          },
          emergencyContact: {
            name: 'Lisa Davis',
            relationship: 'Wife',
            phone: '+1234567895'
          },
          schedule: [
            { dayOfWeek: 1, startTime: '08:00', endTime: '18:00', isWorkingDay: true },
            { dayOfWeek: 2, startTime: '08:00', endTime: '18:00', isWorkingDay: true },
            { dayOfWeek: 3, startTime: '08:00', endTime: '18:00', isWorkingDay: true },
            { dayOfWeek: 4, startTime: '08:00', endTime: '18:00', isWorkingDay: true },
            { dayOfWeek: 5, startTime: '08:00', endTime: '18:00', isWorkingDay: true },
            { dayOfWeek: 6, startTime: '00:00', endTime: '00:00', isWorkingDay: false },
            { dayOfWeek: 0, startTime: '00:00', endTime: '00:00', isWorkingDay: false }
          ],
          performance: {
            ordersServed: 0,
            customerRating: 0,
            punctualityScore: 100,
            salesGenerated: 0,
            lastReviewDate: '2024-01-01'
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 3,
        itemsPerPage: 20,
        hasNextPage: false,
        hasPreviousPage: false
      }
    };
    
    return NextResponse.json(mockData);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/staff`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating staff member:', error);
    
    // Return mock created staff member for development
    const mockStaff = {
      id: `staff-${Date.now()}`,
      firstName: 'New',
      lastName: 'Employee',
      email: '<EMAIL>',
      phone: '+1234567896',
      slug: 'new-employee',
      position: 'Server',
      department: 'Front of House',
      roleId: 'role-2',
      roleName: 'Server',
      permissions: ['view_orders', 'take_orders'],
      status: 'active',
      employeeId: `EMP${Date.now()}`,
      hireDate: new Date().toISOString().split('T')[0],
      hourlyRate: 15.00,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return NextResponse.json(mockStaff);
  }
}
