import { NextRequest, NextResponse } from 'next/server';
import { campaignSegmentService } from '@/services';
import { supabase } from '@/lib/supabase/client';

/**
 * GET /api/merchants/[merchantId]/campaign-segments/[segmentId]/emails
 * Get emails in a campaign segment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; segmentId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get emails in segment
    const emails = await campaignSegmentService.getSegmentEmails(
      params.merchantId,
      params.segmentId
    );

    return NextResponse.json(emails);
  } catch (error) {
    console.error('Error fetching segment emails:', error);
    return NextResponse.json(
      { error: 'Failed to fetch segment emails' },
      { status: 500 }
    );
  }
}
