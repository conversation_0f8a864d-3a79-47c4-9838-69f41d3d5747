/**
 * API route for menu items management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/menu-items${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/branches/[branchId]/menu-items:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch menu items' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/menu-items`, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/branches/[branchId]/menu-items:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create menu item' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
