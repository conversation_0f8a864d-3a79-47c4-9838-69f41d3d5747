import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';

/**
 * POST handler for marking a reservation as no-show
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; reservationId: string } }
) {
  try {
    // Get the merchant ID and reservation ID from the URL
    const { merchantId, reservationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Update the reservation status to no-show
    const reservation = await reservationService.updateReservation(merchantId, reservationId, {
      status: 'no-show'
    });
    
    return NextResponse.json({ 
      success: true, 
      message: 'Reservation marked as no-show',
      reservation 
    });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/reservations/[reservationId]/no-show:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
