import { NextRequest, NextResponse } from 'next/server';
import { voucherService } from '@/services/voucherService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for applying a voucher to an appointment
const applyVoucherSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  customerId: z.string().optional(),
  userId: z.string().optional(),
});

/**
 * POST handler for applying a voucher to an appointment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; appointmentId: string } }
) {
  try {
    // Get the merchant ID and appointment ID from the URL
    const { merchantId, appointmentId } = params;
    
    // Check authentication
    const { authenticated, user } = await checkAuth(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = applyVoucherSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Apply the voucher to the appointment
    const { code, customerId, userId } = validationResult.data;
    const result = await voucherService.applyVoucherToAppointment(
      merchantId,
      appointmentId,
      code,
      customerId,
      userId || user.id
    );
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/appointments/[appointmentId]/apply-voucher:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
