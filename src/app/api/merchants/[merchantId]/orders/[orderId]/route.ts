import { NextRequest, NextResponse } from 'next/server';
import { checkAuth } from '@/lib/auth';

// Mock orders data (imported from the main orders route)
import { orders } from '../route';

/**
 * GET handler for fetching a specific order by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; orderId: string } }
) {
  try {
    // Get the merchant ID and order ID from the URL
    const { merchantId, orderId } = params;

    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get orders for the merchant
    const merchantOrders = orders[merchantId] || [];

    // Find the specific order
    const order = merchantOrders.find(order => order.id === orderId);

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(order);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/orders/[orderId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating an order's status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; orderId: string } }
) {
  try {
    // Get the merchant ID and order ID from the URL
    const { merchantId, orderId } = params;

    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get orders for the merchant
    const merchantOrders = orders[merchantId] || [];

    // Find the specific order
    const orderIndex = merchantOrders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Get the request body
    const body = await request.json();

    // Update the order
    const updatedOrder = {
      ...merchantOrders[orderIndex],
      ...body,
      updatedAt: new Date().toISOString(),
    };

    // If status is being updated to completed, add completedAt timestamp
    if (body.status === 'completed' && merchantOrders[orderIndex].status !== 'completed') {
      updatedOrder.completedAt = new Date().toISOString();
    }

    // Update the order in the mock data
    merchantOrders[orderIndex] = updatedOrder;

    return NextResponse.json(updatedOrder);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/orders/[orderId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for cancelling an order
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; orderId: string } }
) {
  try {
    // Get the merchant ID and order ID from the URL
    const { merchantId, orderId } = params;

    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get orders for the merchant
    const merchantOrders = orders[merchantId] || [];

    // Find the specific order
    const orderIndex = merchantOrders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Get the request body
    const body = await request.json();

    // Update the order status to cancelled
    const updatedOrder = {
      ...merchantOrders[orderIndex],
      status: 'cancelled',
      updatedAt: new Date().toISOString(),
      cancellationReason: body.reason || 'No reason provided',
    };

    // Update the order in the mock data
    merchantOrders[orderIndex] = updatedOrder;

    return NextResponse.json(updatedOrder);
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/orders/[orderId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
