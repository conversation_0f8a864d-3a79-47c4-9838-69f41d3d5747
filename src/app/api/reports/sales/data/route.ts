import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Mock sales data
const generateMockSalesData = (dateRange: string) => {
  const now = new Date();
  const days = dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : 90;
  
  const salesData = [];
  const revenueData = [];
  const topItems = [];
  
  // Generate daily sales data
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    const baseRevenue = 2000 + Math.random() * 2000;
    const orders = Math.floor(50 + Math.random() * 100);
    
    salesData.push({
      date: date.toISOString().split('T')[0],
      revenue: Math.round(baseRevenue),
      orders,
      avgOrderValue: Math.round(baseRevenue / orders * 100) / 100,
    });
    
    revenueData.push({
      date: date.toISOString().split('T')[0],
      revenue: Math.round(baseRevenue),
    });
  }
  
  // Generate top items
  const items = [
    'Margherita Pizza', 'Caesar Salad', 'Grilled Salmon', 'Pasta Carbonara',
    'Chicken Wings', 'Beef Burger', 'Fish & Chips', 'Vegetable Curry'
  ];
  
  items.forEach((item, index) => {
    topItems.push({
      name: item,
      sales: Math.floor(50 + Math.random() * 100),
      revenue: `$${Math.floor(800 + Math.random() * 1200)}`,
      change: `${Math.random() > 0.5 ? '+' : '-'}${Math.floor(Math.random() * 20)}%`,
      trend: Math.random() > 0.3 ? 'up' : 'down',
    });
  });
  
  // Sort by sales
  topItems.sort((a, b) => b.sales - a.sales);
  
  return {
    salesData,
    revenueData,
    topItems: topItems.slice(0, 5),
  };
};

export async function GET(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '30d';
    const branchId = searchParams.get('branchId');

    // Generate mock data
    const { salesData, revenueData, topItems } = generateMockSalesData(dateRange);
    
    // Calculate metrics
    const totalRevenue = salesData.reduce((sum, day) => sum + day.revenue, 0);
    const totalOrders = salesData.reduce((sum, day) => sum + day.orders, 0);
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Calculate changes (comparing to previous period)
    const currentPeriodRevenue = totalRevenue;
    const previousPeriodRevenue = currentPeriodRevenue * (0.85 + Math.random() * 0.3); // Mock previous period
    const revenueChange = ((currentPeriodRevenue - previousPeriodRevenue) / previousPeriodRevenue * 100);
    
    const metrics = {
      totalRevenue: {
        value: `$${totalRevenue.toLocaleString()}`,
        change: `${revenueChange > 0 ? '+' : ''}${revenueChange.toFixed(1)}%`,
        trend: revenueChange > 0 ? 'up' : 'down',
      },
      totalOrders: {
        value: totalOrders.toString(),
        change: '+8.2%',
        trend: 'up',
      },
      avgOrderValue: {
        value: `$${avgOrderValue.toFixed(2)}`,
        change: '-2.3%',
        trend: 'down',
      },
      uniqueCustomers: {
        value: Math.floor(totalOrders * 0.7).toString(),
        change: '+15.7%',
        trend: 'up',
      },
    };

    // Orders by hour (for today)
    const ordersByHour = Array.from({ length: 18 }, (_, i) => {
      const hour = i + 6; // 6 AM to 11 PM
      const hourLabel = hour <= 12 ? `${hour} AM` : `${hour - 12} PM`;
      const isLunchTime = hour >= 12 && hour <= 14;
      const isDinnerTime = hour >= 18 && hour <= 20;
      
      let baseOrders = 15;
      if (isLunchTime) baseOrders = 60;
      if (isDinnerTime) baseOrders = 80;
      
      return {
        hour: hourLabel,
        orders: Math.floor(baseOrders + Math.random() * 30),
      };
    });

    // Payment methods breakdown
    const paymentMethods = [
      { method: 'Credit Card', percentage: 65, amount: totalRevenue * 0.65 },
      { method: 'Cash', percentage: 25, amount: totalRevenue * 0.25 },
      { method: 'Digital Wallet', percentage: 10, amount: totalRevenue * 0.10 },
    ];

    // Order types breakdown
    const orderTypes = [
      { type: 'Dine-in', percentage: 45, count: Math.floor(totalOrders * 0.45) },
      { type: 'Takeaway', percentage: 35, count: Math.floor(totalOrders * 0.35) },
      { type: 'Delivery', percentage: 20, count: Math.floor(totalOrders * 0.20) },
    ];

    return NextResponse.json({
      metrics,
      salesData,
      revenueData,
      topItems,
      ordersByHour,
      paymentMethods,
      orderTypes,
      dateRange,
      generatedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Sales data API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
