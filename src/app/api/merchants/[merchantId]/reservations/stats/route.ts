import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';
import { subDays, format } from 'date-fns';

/**
 * GET handler for fetching reservation statistics
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30d';
    
    // Calculate date range based on period
    let startDate: Date;
    const endDate = new Date();
    
    switch (period) {
      case '7d':
        startDate = subDays(endDate, 7);
        break;
      case '30d':
        startDate = subDays(endDate, 30);
        break;
      case '90d':
        startDate = subDays(endDate, 90);
        break;
      case '1y':
        startDate = subDays(endDate, 365);
        break;
      default:
        startDate = subDays(endDate, 30);
    }
    
    // Get all reservations for the merchant
    const allReservations = await reservationService.getReservations(merchantId);
    
    // Filter reservations by date range
    const startDateStr = format(startDate, 'yyyy-MM-dd');
    const endDateStr = format(endDate, 'yyyy-MM-dd');
    
    const reservationsInPeriod = allReservations.filter(reservation => 
      reservation.date >= startDateStr && reservation.date <= endDateStr
    );
    
    // Get today's date
    const today = format(new Date(), 'yyyy-MM-dd');
    
    // Calculate statistics
    const totalReservations = reservationsInPeriod.length;
    const todayReservations = reservationsInPeriod.filter(r => r.date === today).length;
    const upcomingReservations = reservationsInPeriod.filter(r => 
      r.date >= today && (r.status === 'pending' || r.status === 'confirmed')
    ).length;
    const completedReservations = reservationsInPeriod.filter(r => 
      r.status === 'completed'
    ).length;
    const cancelledReservations = reservationsInPeriod.filter(r => 
      r.status === 'cancelled'
    ).length;
    const noShowReservations = reservationsInPeriod.filter(r => 
      r.status === 'no-show'
    ).length;
    
    // Calculate no-show rate
    const totalCompletedOrNoShow = completedReservations + noShowReservations;
    const noShowRate = totalCompletedOrNoShow > 0 
      ? (noShowReservations / totalCompletedOrNoShow) * 100 
      : 0;
    
    // Status counts
    const statusCounts = reservationsInPeriod.reduce((acc, reservation) => {
      acc[reservation.status] = (acc[reservation.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // Monthly stats for the period
    const monthlyStats: Array<{ month: string; count: number }> = [];
    const monthsInPeriod = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
    
    for (let i = 0; i < Math.min(monthsInPeriod, 12); i++) {
      const monthStart = subDays(endDate, (i + 1) * 30);
      const monthEnd = subDays(endDate, i * 30);
      const monthStartStr = format(monthStart, 'yyyy-MM-dd');
      const monthEndStr = format(monthEnd, 'yyyy-MM-dd');
      
      const monthReservations = allReservations.filter(r => 
        r.date >= monthStartStr && r.date <= monthEndStr
      );
      
      monthlyStats.unshift({
        month: format(monthStart, 'MMM yyyy'),
        count: monthReservations.length
      });
    }
    
    const stats = {
      totalReservations,
      todayReservations,
      upcomingReservations,
      completedReservations,
      cancelledReservations,
      noShowRate: Math.round(noShowRate * 100) / 100,
      statusCounts,
      monthlyStats,
      period,
      dateRange: {
        start: startDateStr,
        end: endDateStr
      }
    };
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/reservations/stats:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
