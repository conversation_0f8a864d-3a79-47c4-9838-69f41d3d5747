import { NextRequest, NextResponse } from 'next/server';
import { serviceService } from '@/services/serviceService';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * GET handler for checking service availability
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; serviceId: string } }
) {
  try {
    // Get the merchant ID and service ID from the URL
    const { merchantId, serviceId } = params;
    
    // Get the date from the query parameters
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    
    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      );
    }
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get availability for the service on the specified date
    const availability = await serviceService.getAvailability(merchantId, serviceId, date);
    
    return NextResponse.json(availability);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/services/[serviceId]/availability:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
