import { NextRequest, NextResponse } from 'next/server';
import { notificationService } from '@/services/notificationService';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * PUT handler for marking a notification as read
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; notificationId: string } }
) {
  try {
    // Get the notification ID from the URL
    const { notificationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Mark the notification as read
    const notification = await notificationService.markAsRead(notificationId);
    
    return NextResponse.json(notification);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/notifications/[notificationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a notification
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; notificationId: string } }
) {
  try {
    // Get the notification ID from the URL
    const { notificationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the notification
    await notificationService.deleteNotification(notificationId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/notifications/[notificationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
