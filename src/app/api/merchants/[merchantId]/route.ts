/**
 * API route for individual merchant management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string }> }
) {
  try {
    const { merchantId } = await params;
    
    const response = await fetchClient(`/merchants/${merchantId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch merchant' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string }> }
) {
  try {
    const { merchantId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}`, {
      method: 'PUT',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update merchant' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string }> }
) {
  try {
    const { merchantId } = await params;
    
    const response = await fetchClient(`/merchants/${merchantId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete merchant' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
