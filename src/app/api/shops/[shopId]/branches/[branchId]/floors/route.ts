/**
 * API route for shop branch floors management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/shops/${shopId}/branches/${branchId}/floors${queryString ? `?${queryString}` : ''}`;

    console.log('Fetching floors from backend:', url);

    const response = await serverFetchClient(url, request, {
      method: 'GET',
    });

    console.log('Backend response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error response:', errorText);
      throw new Error(`Backend responded with status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Successfully parsed floors data:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching floors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch floors', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ shopId: string; branchId: string }> }
) {
  try {
    const { shopId, branchId } = await params;
    const body = await request.json();
    const url = `/shops/${shopId}/branches/${branchId}/floors`;

    const response = await serverFetchClient(url, request, {
      method: 'POST',
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating floor:', error);
    return NextResponse.json(
      { error: 'Failed to create floor' },
      { status: 500 }
    );
  }
}
