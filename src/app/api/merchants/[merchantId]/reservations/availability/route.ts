import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';

/**
 * GET handler for checking table availability
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const partySize = searchParams.get('partySize');
    const time = searchParams.get('time');
    const tableId = searchParams.get('tableId');
    
    if (!date || !partySize) {
      return NextResponse.json(
        { error: 'Date and party size are required' },
        { status: 400 }
      );
    }
    
    const partySizeNum = parseInt(partySize, 10);
    if (isNaN(partySizeNum)) {
      return NextResponse.json(
        { error: 'Invalid party size' },
        { status: 400 }
      );
    }
    
    if (time && tableId) {
      // Check specific table availability
      const isAvailable = await reservationService.checkTableAvailability(
        merchantId,
        tableId,
        date,
        time,
        60 // Default 1 hour duration
      );
      
      return NextResponse.json({
        available: isAvailable,
        date,
        time,
        tableId,
        partySize: partySizeNum
      });
    } else {
      // Get available time slots
      const timeSlots = await reservationService.getAvailableTimeSlots(
        merchantId,
        date,
        partySizeNum
      );
      
      return NextResponse.json({
        date,
        partySize: partySizeNum,
        availableSlots: timeSlots
      });
    }
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/reservations/availability:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
