/**
 * API route for individual review management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reviewId: string }> }
) {
  try {
    const { merchantId, branchId, reviewId } = await params;
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/branches/[branchId]/reviews/[reviewId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch review' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reviewId: string }> }
) {
  try {
    const { merchantId, branchId, reviewId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}`, {
      method: 'PUT',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/branches/[branchId]/reviews/[reviewId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update review' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
