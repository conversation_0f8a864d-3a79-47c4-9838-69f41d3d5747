/**
 * API route for shop management (replaces merchant management)
 * Forwards requests to the Golang backend using shop endpoints
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/shops${queryString ? `?${queryString}` : ''}`;

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants:', error);

    // Return proper error response - no mock data fallback

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch shops' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const response = await serverFetchClient('/shops', request, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/merchants:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create shop' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
