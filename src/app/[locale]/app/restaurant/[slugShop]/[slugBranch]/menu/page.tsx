'use client';

import React, { useState, useMemo } from 'react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetMenuItemsQuery, MenuItem, MenuItemFilters } from '@/lib/redux/api/endpoints/menuApi';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import {
  MenuHeader,
  MenuSearch,
  MenuViewToggle,
  MenuTableView,
  MenuGridView,
  MenuPagination,
  MenuSort
} from './_components';

interface MenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function MenuPage({ params }: MenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'created_at' | 'updated_at' | 'category'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  // Create filters object for backend
  const filters = useMemo((): MenuItemFilters => ({
    search: searchTerm || undefined,
    sort_by: sortBy,
    sort_order: sortOrder,
    page: currentPage,
    limit: 20
  }), [searchTerm, sortBy, sortOrder, currentPage]);

  // Get menu items from backend with filters
  const {
    data: menuItemsResponse,
    isLoading: isLoadingMenuItems,
    isError: isMenuItemsError
  } = useGetMenuItemsQuery(
    merchant?.id && branch?.id ? {
      shopId: merchant.id,
      branchId: branch.id,
      filters
    } : { shopId: 'skip' }, // Provide a default value to avoid undefined
    {
      skip: !merchant?.id || !branch?.id
    }
  );

  const isLoading = isLoadingMerchants || isLoadingMenuItems;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!merchant || !branch) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#81766a] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  if (isMenuItemsError) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Error Loading Menu</h1>
          <p className="text-[#81766a] text-sm">There was an error loading the menu items. Please try again later.</p>
        </div>
      </div>
    );
  }

  // Extract menu items and pagination info from backend response
  const menuItems: MenuItem[] = menuItemsResponse?.data || [];
  const totalItems = menuItemsResponse?.total || 0;
  const totalPages = menuItemsResponse?.total_pages || 1;

  // No need for frontend filtering - it's done on the backend
  const filteredItems = menuItems;

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden font-be-vietnam">
      <div className="layout-container flex h-full grow flex-col">
        <div className="gap-1 px-6 flex flex-1 justify-center py-5">

          {/* Main Content */}
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <MenuHeader slugShop={slugShop} slugBranch={slugBranch} />

            <div className="flex items-center justify-between gap-4 px-4 py-4">
              <MenuSearch
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
              />
              <MenuSort
                sortBy={sortBy}
                sortOrder={sortOrder}
                onSortChange={(newSortBy, newSortOrder) => {
                  setSortBy(newSortBy);
                  setSortOrder(newSortOrder);
                  setCurrentPage(1); // Reset to first page when sorting changes
                }}
              />
            </div>

            {/* Menu Items Header */}
            <div className="flex items-center justify-between px-4 pb-3 pt-5">
              <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em]">Menu Items</h2>
              <MenuViewToggle
                viewMode={viewMode}
                onViewModeChange={setViewMode}
              />
            </div>

            {/* Content based on view mode */}
            {viewMode === 'table' ? (
              <MenuTableView
                items={filteredItems}
                slugShop={slugShop}
                slugBranch={slugBranch}
                searchTerm={searchTerm}
              />
            ) : (
              <MenuGridView
                items={filteredItems}
                slugShop={slugShop}
                slugBranch={slugBranch}
                searchTerm={searchTerm}
              />
            )}

            {/* Pagination */}
            <MenuPagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              itemsPerPage={20}
              onPageChange={(page) => {
                setCurrentPage(page);
                // Scroll to top when page changes
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
