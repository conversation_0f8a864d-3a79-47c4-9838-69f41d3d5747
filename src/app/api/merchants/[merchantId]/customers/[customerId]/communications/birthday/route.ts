import { NextRequest, NextResponse } from 'next/server';
import { customerCommunicationService } from '@/services';
import { z } from 'zod';
import { supabase } from '@/lib/supabase/client';

// Schema for sending a birthday greeting
const sendBirthdayGreetingSchema = z.object({
  type: z.enum(['email', 'sms']).default('email'),
});

/**
 * POST /api/merchants/[merchantId]/customers/[customerId]/communications/birthday
 * Send a birthday greeting to a customer
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; customerId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = sendBirthdayGreetingSchema.parse(body);

    // Send birthday greeting
    const communication = await customerCommunicationService.sendBirthdayGreeting(
      params.merchantId,
      params.customerId,
      validatedData.type
    );

    return NextResponse.json(communication);
  } catch (error) {
    console.error('Error sending birthday greeting:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to send birthday greeting', message: error.message },
      { status: 500 }
    );
  }
}
