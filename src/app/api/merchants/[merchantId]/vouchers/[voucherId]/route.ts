import { NextRequest, NextResponse } from 'next/server';
import { voucherService } from '@/services/voucherService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a voucher
const updateVoucherSchema = z.object({
  code: z.string().min(3, 'Code must be at least 3 characters').optional(),
  type: z.enum(['percentage', 'fixed_amount', 'free_service']).optional(),
  value: z.number().positive('Value must be positive').optional(),
  minSpend: z.number().optional(),
  maxDiscount: z.number().optional(),
  serviceId: z.string().optional(),
  categoryId: z.string().optional(),
  startDate: z.string().transform(val => new Date(val)).optional(),
  endDate: z.string().transform(val => new Date(val)).optional(),
  usageLimit: z.number().int().optional(),
  isActive: z.boolean().optional(),
  isGiftCard: z.boolean().optional(),
  recipientEmail: z.string().email().optional(),
  recipientName: z.string().optional(),
  senderName: z.string().optional(),
  message: z.string().optional(),
});

/**
 * GET handler for fetching a specific voucher
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; voucherId: string } }
) {
  try {
    // Get the merchant ID and voucher ID from the URL
    const { merchantId, voucherId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the voucher
    const voucher = await voucherService.getVoucherById(merchantId, voucherId);
    
    return NextResponse.json(voucher);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/vouchers/[voucherId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a voucher
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; voucherId: string } }
) {
  try {
    // Get the merchant ID and voucher ID from the URL
    const { merchantId, voucherId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateVoucherSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the voucher
    const voucher = await voucherService.updateVoucher(
      merchantId,
      voucherId,
      validationResult.data
    );
    
    return NextResponse.json(voucher);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/vouchers/[voucherId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a voucher
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; voucherId: string } }
) {
  try {
    // Get the merchant ID and voucher ID from the URL
    const { merchantId, voucherId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the voucher
    const result = await voucherService.deleteVoucher(merchantId, voucherId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/vouchers/[voucherId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
