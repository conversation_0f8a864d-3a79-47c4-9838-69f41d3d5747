import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';
import { z } from 'zod';

const updateStatusSchema = z.object({
  status: z.enum(['pending', 'confirmed', 'cancelled', 'completed', 'no-show', 'seated']),
});

/**
 * PATCH handler for updating reservation status
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { merchantId: string; reservationId: string } }
) {
  try {
    // Get the merchant ID and reservation ID from the URL
    const { merchantId, reservationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateStatusSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { status } = validationResult.data;
    
    // Update the reservation status
    const reservation = await reservationService.updateReservation(merchantId, reservationId, {
      status
    });
    
    return NextResponse.json(reservation);
  } catch (error) {
    console.error('Error in PATCH /api/merchants/[merchantId]/reservations/[reservationId]/status:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
