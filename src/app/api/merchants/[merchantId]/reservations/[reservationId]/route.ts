import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { updateReservationSchema } from '@/lib/validations/reservationSchema';
import { checkAuth } from '@/lib/auth';

/**
 * GET handler for fetching a specific reservation
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; reservationId: string } }
) {
  try {
    // Get the merchant ID and reservation ID from the URL
    const { merchantId, reservationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the reservation
    const reservation = await reservationService.getReservationById(merchantId, reservationId);
    
    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(reservation);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/reservations/[reservationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a reservation
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; reservationId: string } }
) {
  try {
    // Get the merchant ID and reservation ID from the URL
    const { merchantId, reservationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updateReservationSchema.safeParse({
      ...body,
      id: reservationId,
      merchantId,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the reservation
    const reservation = await reservationService.updateReservation(merchantId, reservationId, body);
    
    return NextResponse.json(reservation);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/reservations/[reservationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a reservation
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; reservationId: string } }
) {
  try {
    // Get the merchant ID and reservation ID from the URL
    const { merchantId, reservationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Delete the reservation
    await reservationService.deleteReservation(merchantId, reservationId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/reservations/[reservationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
