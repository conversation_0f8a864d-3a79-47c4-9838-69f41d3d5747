import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { checkAuth } from '@/lib/auth';
import { z } from 'zod';

const confirmationSchema = z.object({
  method: z.enum(['email', 'sms']),
});

/**
 * POST handler for sending reservation confirmation
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string; reservationId: string } }
) {
  try {
    // Get the merchant ID and reservation ID from the URL
    const { merchantId, reservationId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = confirmationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { method } = validationResult.data;
    
    // Get the reservation details
    const reservation = await reservationService.getReservationById(merchantId, reservationId);
    
    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }
    
    // TODO: Implement actual email/SMS sending logic
    // For now, we'll just simulate the confirmation
    console.log(`Sending ${method} confirmation for reservation ${reservationId} to ${
      method === 'email' ? reservation.customerEmail : reservation.customerPhone
    }`);
    
    // Simulate sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return NextResponse.json({ 
      success: true, 
      message: `Confirmation sent via ${method}`,
      method,
      recipient: method === 'email' ? reservation.customerEmail : reservation.customerPhone
    });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/reservations/[reservationId]/confirm:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
