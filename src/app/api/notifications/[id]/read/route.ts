import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Mock data - in a real app, this would be in a database
let mockNotifications = [
  {
    id: '1',
    type: 'order',
    title: 'New Order Received',
    message: 'Order #1234 from <PERSON> - $45.99',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    priority: 'high',
    actionUrl: '/app/restaurant/orders/1234',
    actionLabel: 'View Order',
    metadata: { orderId: '1234', amount: 45.99 }
  },
  // ... other notifications
];

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Find notification
    const notificationIndex = mockNotifications.findIndex(n => n.id === id);
    if (notificationIndex === -1) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Mark as read
    mockNotifications[notificationIndex].read = true;

    // In a real app, update database
    // await db.notification.update({
    //   where: { id },
    //   data: { read: true, readAt: new Date() }
    // });

    return NextResponse.json({
      message: 'Notification marked as read',
      notification: mockNotifications[notificationIndex]
    });

  } catch (error) {
    console.error('Mark as read error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
