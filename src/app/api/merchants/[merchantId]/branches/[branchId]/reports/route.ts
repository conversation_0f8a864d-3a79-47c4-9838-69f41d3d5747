/**
 * API route for branch reports
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/reports${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching branch reports:', error);
    
    // Return mock data for development
    const mockData = {
      salesTrends: [
        { period: '2024-01-01', sales: 18500, orders: 125, date: '2024-01-01', name: 'Week 1' },
        { period: '2024-01-08', sales: 12300, orders: 89, date: '2024-01-08', name: 'Week 2' },
        { period: '2024-01-15', sales: 15800, orders: 102, date: '2024-01-15', name: 'Week 3' },
        { period: '2024-01-22', sales: 25450, orders: 156, date: '2024-01-22', name: 'Week 4' },
      ],
      popularItems: [
        {
          id: '1',
          name: 'Spicy Chicken Sandwich',
          category: 'Main Course',
          orders: 350,
          revenue: 3500,
          image: '/images/menu/chicken-sandwich.jpg',
          price: 10.00
        },
        {
          id: '2',
          name: 'Avocado Toast',
          category: 'Appetizer',
          orders: 280,
          revenue: 2240,
          image: '/images/menu/avocado-toast.jpg',
          price: 8.00
        },
        {
          id: '3',
          name: 'Classic Burger',
          category: 'Main Course',
          orders: 250,
          revenue: 2500,
          image: '/images/menu/classic-burger.jpg',
          price: 10.00
        },
        {
          id: '4',
          name: 'Caesar Salad',
          category: 'Appetizer',
          orders: 220,
          revenue: 1760,
          image: '/images/menu/caesar-salad.jpg',
          price: 8.00
        },
        {
          id: '5',
          name: 'Chocolate Brownie',
          category: 'Dessert',
          orders: 200,
          revenue: 1200,
          image: '/images/menu/chocolate-brownie.jpg',
          price: 6.00
        },
      ],
      customerAnalytics: {
        totalCustomers: 1200,
        newCustomers: 180,
        returningCustomers: 1020,
        averageSpend: 21.21,
        repeatCustomerRate: 45.5,
        customerLifetimeValue: 156.78
      },
      revenueAnalytics: {
        totalRevenue: 72050,
        revenueGrowth: 12.5,
        averageOrderValue: 21.21,
        totalOrders: 472,
        orderGrowth: 8.3
      },
      period: 'month',
      generatedAt: new Date().toISOString()
    };
    
    return NextResponse.json(mockData);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reports/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error exporting reports:', error);
    
    // Return mock export URL for development
    return NextResponse.json({
      downloadUrl: '/api/mock-export/reports.pdf'
    });
  }
}
