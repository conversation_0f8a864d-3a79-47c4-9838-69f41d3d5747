import { NextRequest, NextResponse } from 'next/server';
import { customerService } from '@/services/customerService';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * GET handler for fetching a customer by user ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; userId: string } }
) {
  try {
    // Get the merchant ID and user ID from the URL
    const { merchantId, userId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the customer by user ID
    const customer = await customerService.getCustomerByUserId(merchantId, userId);
    
    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/customers/user/[userId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
