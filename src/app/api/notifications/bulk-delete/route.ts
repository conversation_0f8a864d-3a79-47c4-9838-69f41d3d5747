import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Mock data - in a real app, this would be in a database
let mockNotifications = [
  {
    id: '1',
    type: 'order',
    title: 'New Order Received',
    message: 'Order #1234 from <PERSON> - $45.99',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    priority: 'high',
    actionUrl: '/app/restaurant/orders/1234',
    actionLabel: 'View Order',
    metadata: { orderId: '1234', amount: 45.99 }
  },
  {
    id: '2',
    type: 'reservation',
    title: 'New Reservation',
    message: 'Table for 4 at 7:00 PM today - <PERSON>',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    read: false,
    priority: 'medium',
    actionUrl: '/app/restaurant/reservations',
    actionLabel: 'View Reservation'
  },
  // ... other notifications
];

export async function DELETE(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { ids } = body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Invalid or empty ids array' },
        { status: 400 }
      );
    }

    // Find notifications to delete
    const notificationsToDelete = mockNotifications.filter(n => ids.includes(n.id));
    const deletedCount = notificationsToDelete.length;

    // Remove notifications
    mockNotifications = mockNotifications.filter(n => !ids.includes(n.id));

    // In a real app, delete from database
    // const result = await db.notification.deleteMany({
    //   where: { 
    //     id: { in: ids },
    //     userId: session.user.id
    //   }
    // });

    return NextResponse.json({
      message: `${deletedCount} notifications deleted successfully`,
      deletedCount,
      deletedNotifications: notificationsToDelete,
      remainingNotifications: mockNotifications
    });

  } catch (error) {
    console.error('Bulk delete error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
