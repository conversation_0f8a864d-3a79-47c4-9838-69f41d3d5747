import { NextRequest, NextResponse } from 'next/server';
import { communicationAnalyticsService } from '@/services';
import { supabase } from '@/lib/supabase/client';

/**
 * GET /api/merchants/[merchantId]/campaigns/[campaignId]/analytics
 * Get analytics for a specific campaign
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; campaignId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get analytics
    const analytics = await communicationAnalyticsService.getCampaignAnalytics(
      params.merchantId,
      params.campaignId
    );

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching campaign analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaign analytics' },
      { status: 500 }
    );
  }
}
