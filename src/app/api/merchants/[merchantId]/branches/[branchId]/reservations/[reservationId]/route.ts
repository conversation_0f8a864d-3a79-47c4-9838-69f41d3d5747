/**
 * API route for individual reservation management
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reservationId: string }> }
) {
  try {
    const { merchantId, branchId, reservationId } = await params;
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/branches/[branchId]/reservations/[reservationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch reservation' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reservationId: string }> }
) {
  try {
    const { merchantId, branchId, reservationId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}`, {
      method: 'PUT',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/branches/[branchId]/reservations/[reservationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update reservation' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string; reservationId: string }> }
) {
  try {
    const { merchantId, branchId, reservationId } = await params;
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await handleApiResponse(response);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/branches/[branchId]/reservations/[reservationId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to cancel reservation' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
