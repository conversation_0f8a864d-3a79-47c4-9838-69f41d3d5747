/**
 * API route for shop dashboard statistics using slug
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopSlug: string }> }
) {
  try {
    const { shopSlug } = await params;
    const { searchParams } = new URL(request.url);
    const branchSlug = searchParams.get('branchSlug') || 'downtown';

    // Use shop slug to get dashboard stats - match backend route structure
    const url = `/shops/slug/${shopSlug}/branches/${branchSlug}/dashboard/stats`;

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/shops/slug/[shopSlug]/dashboard/stats:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch dashboard stats' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
