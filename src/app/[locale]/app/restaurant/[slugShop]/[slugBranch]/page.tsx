'use client';

import { useEffect } from 'react';
import { useRouter } from '@/i18n/navigation';
import { AppLoading } from '@/components/ui/app-loading';
import { getBranchWithShop } from '@/mock/shopData';
import { use } from 'react';

interface BranchPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function BranchPage({ params }: BranchPageProps) {
  const { slugShop, slugBranch } = use(params);
  const router = useRouter();

  useEffect(() => {
    // Verify that the branch exists
    const branchWithShop = getBranchWithShop(slugShop, slugBranch);

    if (branchWithShop) {
      // Redirect to the dashboard as the main branch page
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/dashboard`);
    } else {
      // If branch doesn't exist, redirect back to the shop page
      router.push(`/app/restaurant/${slugShop}`);
    }
  }, [router, slugShop, slugBranch]);

  // Show loading while redirecting
  return <AppLoading type="restaurant" size="lg" />;
}
