import { NextRequest, NextResponse } from 'next/server';
import { paymentService } from '@/services/paymentService';
import { supabase } from '@/lib/supabase/client';
import { z } from 'zod';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

// Schema for updating a payment intent
const updatePaymentIntentSchema = z.object({
  status: z.enum([
    'requires_payment_method',
    'requires_confirmation',
    'requires_action',
    'processing',
    'succeeded',
    'canceled'
  ]),
});

/**
 * GET handler for fetching a specific payment intent
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; intentId: string } }
) {
  try {
    // Get the merchant ID and intent ID from the URL
    const { merchantId, intentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the payment intent
    const paymentIntent = await paymentService.getPaymentIntent(merchantId, intentId);
    
    return NextResponse.json(paymentIntent);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/payment-intents/[intentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a payment intent
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; intentId: string } }
) {
  try {
    // Get the merchant ID and intent ID from the URL
    const { merchantId, intentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = updatePaymentIntentSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the payment intent
    const paymentIntent = await paymentService.updatePaymentIntent(
      merchantId,
      intentId,
      validationResult.data.status
    );
    
    return NextResponse.json(paymentIntent);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/payment-intents/[intentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
