/**
 * API route for purchase orders management
 * Forwards requests to the Golang backend with consistent filtering, sorting, and pagination
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    // Construct the backend URL
    let url = `/shops/${shopId}/branches/${branchId}/purchase-orders`;

    // Add any additional query parameters with defaults
    const otherParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      if (key !== 'shopId' && key !== 'branchId') {
        otherParams.append(key, value);
      }
    });

    // Add default pagination parameters if not provided
    if (!otherParams.has('page')) {
      otherParams.append('page', '1');
    }
    if (!otherParams.has('limit')) {
      otherParams.append('limit', '20');
    }

    if (otherParams.toString()) {
      url += `?${otherParams.toString()}`;
    }

    console.log('Fetching purchase orders from backend:', {
      fullUrl: url,
      shopId,
      branchId,
      queryParams: otherParams.toString()
    });

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/purchase-orders:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch purchase orders' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { shopId, branchId, ...purchaseOrderData } = body;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/purchase-orders`;

    console.log('Creating purchase order:', {
      url,
      shopId,
      branchId,
      data: purchaseOrderData
    });

    const response = await serverFetchClient(url, request, {
      method: 'POST',
      body: JSON.stringify(purchaseOrderData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/purchase-orders:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create purchase order' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
