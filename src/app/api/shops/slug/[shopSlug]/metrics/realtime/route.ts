/**
 * API route for shop real-time metrics using slug
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shopSlug: string }> }
) {
  try {
    const { shopSlug } = await params;
    const { searchParams } = new URL(request.url);
    const branchSlug = searchParams.get('branchSlug') || 'downtown';

    // Use shop slug to get real-time metrics - match backend route structure
    const url = `/shops/slug/${shopSlug}/branches/${branchSlug}/metrics/realtime`;

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/shops/slug/[shopSlug]/metrics/realtime:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch real-time metrics' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
