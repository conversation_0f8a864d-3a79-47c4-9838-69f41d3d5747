import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';



/**
 * GET handler for fetching appointments
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string }> }
) {
  try {
    const { merchantId } = await params;
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/appointments${queryString ? `?${queryString}` : ''}`;

    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/appointments:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch appointments' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

/**
 * POST handler for creating an appointment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string }> }
) {
  try {
    const { merchantId } = await params;
    const body = await request.json();

    const response = await fetchClient(`/merchants/${merchantId}/appointments`, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/appointments:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create appointment' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
